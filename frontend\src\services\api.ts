import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // 统一错误处理
    const message = error.response?.data?.error || error.message || '请求失败'
    console.error('API Error:', message)
    throw new Error(message)
  }
)

// 装备相关API
export const equipmentApi = {
  // 获取装备列表
  getEquipmentList: (params?: {
    equipment_type?: string
    min_level?: number
    max_level?: number
    quality?: string
    limit?: number
  }) => {
    return api.get('/equipment/list', { params })
  },

  // 获取装备详情
  getEquipmentDetail: (equipmentId: string) => {
    return api.get(`/equipment/${equipmentId}`)
  },

  // 分析装备
  analyzeEquipment: (data: {
    equipment: any
    scenario: 'pvp' | 'pve'
    profession: string
  }) => {
    return api.post('/equipment/analyze', data)
  },

  // 对比装备
  compareEquipment: (data: {
    equipment_a: any
    equipment_b: any
    scenario: 'pvp' | 'pve'
    profession: string
  }) => {
    return api.post('/equipment/compare', data)
  },

  // 获取装备推荐
  getEquipmentRecommendations: (params: {
    profession: string
    scenario: 'pvp' | 'pve'
    min_level?: number
    max_level?: number
    equipment_type?: string
  }) => {
    return api.get('/equipment/recommend', { params })
  },
}

export default api
