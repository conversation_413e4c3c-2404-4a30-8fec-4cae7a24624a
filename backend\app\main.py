#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逆水寒智能装备助手 - 主程序
真实产品版本，非demo
"""
import sys
import os
# 确保UTF-8编码
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent  # backend/app/main.py -> nsh_agent/
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, HTMLResponse
import uvicorn
from loguru import logger

from app.core.config import get_settings
from app.api import equipment
from app.services.ai_service import AIService
from app.services.search_service import SearchService
from app.services.learning_service import LearningService

# 获取配置
settings = get_settings()

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    description="基于AI的逆水寒游戏装备分析系统",
    version=settings.APP_VERSION,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
ai_service = AIService()
search_service = SearchService()
learning_service = LearningService(ai_service, search_service)

# 挂载静态文件
static_dir = project_root / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# 注册路由
app.include_router(equipment.router, prefix=settings.API_V1_STR)

# 注册学习Agent路由
from app.api import learning, social_knowledge
app.include_router(learning.router, prefix=settings.API_V1_STR)

# 注册社交知识库路由
app.include_router(social_knowledge.router, prefix=f"{settings.API_V1_STR}/social-knowledge", tags=["社交知识库"])

@app.get("/")
async def serve_frontend():
    """提供前端页面"""
    static_file = project_root / "static" / "index.html"
    if static_file.exists():
        return FileResponse(str(static_file))
    else:
        return {
            "message": f"欢迎使用{settings.APP_NAME}",
            "version": settings.APP_VERSION,
            "status": "running",
            "docs": "/docs",
            "api": settings.API_V1_STR,
            "agent_chat": "/frontend/agent_chat.html"
        }

@app.get("/frontend/agent_chat.html")
async def serve_agent_chat():
    """提供Agent聊天界面"""
    chat_file = project_root / "frontend" / "agent_chat.html"
    if chat_file.exists():
        return FileResponse(str(chat_file), media_type="text/html")
    else:
        return HTMLResponse("""
        <html>
        <head><title>Agent聊天界面未找到</title></head>
        <body>
        <h1>Agent聊天界面未找到</h1>
        <p>文件路径: {}</p>
        <p>请检查前端文件是否存在</p>
        </body>
        </html>
        """.format(chat_file))

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": settings.APP_VERSION,
        "services": {
            "ai_service": ai_service.is_available(),
            "search_service": search_service.is_available(),
            "learning_service": learning_service.is_available()
        }
    }

# 旧的learning API已移动到 app/api/learning.py 中的路由器

# 异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return {
        "error": True,
        "status_code": exc.status_code,
        "message": exc.detail,
        "timestamp": learning_service.get_current_time()
    }

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {type(exc).__name__} - {str(exc)}")
    return {
        "error": True,
        "status_code": 500,
        "message": "内部服务器错误",
        "timestamp": learning_service.get_current_time()
    }

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("正在启动逆水寒智能装备助手...")
    
    # 初始化服务
    await ai_service.initialize()
    await search_service.initialize()
    await learning_service.initialize()

    # 初始化社交知识库服务
    from app.services.social_knowledge_service import social_knowledge_service
    await social_knowledge_service.initialize()

    logger.info("所有服务初始化完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("正在关闭逆水寒智能装备助手...")
    
    # 清理资源
    await learning_service.cleanup()
    await search_service.cleanup()
    await ai_service.cleanup()
    
    logger.info("应用已安全关闭")

def main():
    """主函数"""
    logger.info("🚀 启动逆水寒智能装备助手...")
    logger.info(f"📱 应用版本: {settings.APP_VERSION}")
    logger.info(f"🔧 后端API: http://localhost:8000")
    logger.info(f"📚 API文档: http://localhost:8000/docs")
    logger.info(f"🤖 学习Agent: http://localhost:8000/api/v1/learning/status")
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug"
    )

if __name__ == "__main__":
    main()
