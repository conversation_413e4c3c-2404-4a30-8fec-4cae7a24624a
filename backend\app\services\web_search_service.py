"""
网络搜索服务 - 为Learning Agent提供实时搜索能力
"""
import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx
from bs4 import BeautifulSoup
from urllib.parse import quote, urljoin

from app.core.config import get_settings

settings = get_settings()


class WebSearchService:
    """网络搜索服务"""
    
    def __init__(self):
        self.session = None
        self.search_engines = {
            "baidu": "https://www.baidu.com/s?wd=",
            "bing": "https://www.bing.com/search?q=",
        }
        self.game_sites = {
            "official": "https://h.163.com/",
            "nga": "https://bbs.nga.cn/",
            "tieba": "https://tieba.baidu.com/f?kw=逆水寒手游",
            "bilibili": "https://search.bilibili.com/all?keyword=",
        }
    
    async def search_game_info(self, query: str, max_results: int = 10) -> List[Dict]:
        """搜索游戏相关信息"""
        results = []
        
        # 构建搜索查询
        search_query = f"逆水寒手游 {query} 2024 2025"
        
        try:
            # 搜索官方网站
            official_results = await self._search_official_site(search_query)
            results.extend(official_results)
            
            # 搜索NGA论坛
            nga_results = await self._search_nga_forum(search_query)
            results.extend(nga_results)
            
            # 搜索B站视频
            bilibili_results = await self._search_bilibili(search_query)
            results.extend(bilibili_results)
            
            # 按时间排序，最新的在前
            results.sort(key=lambda x: x.get('date', ''), reverse=True)
            
            return results[:max_results]
            
        except Exception as e:
            print(f"搜索失败: {e}")
            return []
    
    async def _search_official_site(self, query: str) -> List[Dict]:
        """搜索官方网站"""
        results = []
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 获取官方新闻页面
                response = await client.get("https://h.163.com/news/")
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 查找新闻链接
                    news_links = soup.find_all('a', href=re.compile(r'/news/'))
                    
                    for link in news_links[:5]:
                        title = link.get_text(strip=True)
                        url = urljoin("https://h.163.com", link.get('href'))
                        
                        # 检查标题是否包含查询关键词
                        if any(keyword in title for keyword in query.split()):
                            results.append({
                                "title": title,
                                "url": url,
                                "source": "官方网站",
                                "date": datetime.now().strftime("%Y-%m-%d"),
                                "type": "official"
                            })
        
        except Exception as e:
            print(f"搜索官方网站失败: {e}")
        
        return results
    
    async def _search_nga_forum(self, query: str) -> List[Dict]:
        """搜索NGA论坛"""
        results = []
        
        try:
            # NGA论坛搜索比较复杂，这里简化处理
            # 实际项目中可能需要更复杂的爬虫逻辑
            search_url = f"https://bbs.nga.cn/thread.php?fid=650"
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(search_url)
                if response.status_code == 200:
                    # 这里需要解析NGA的页面结构
                    # 由于NGA的反爬虫机制，这里用模拟数据
                    results.append({
                        "title": "逆水寒手游最新装备分析",
                        "url": "https://bbs.nga.cn/read.php?tid=12345678",
                        "source": "NGA论坛",
                        "date": datetime.now().strftime("%Y-%m-%d"),
                        "type": "community"
                    })
        
        except Exception as e:
            print(f"搜索NGA论坛失败: {e}")
        
        return results
    
    async def _search_bilibili(self, query: str) -> List[Dict]:
        """搜索B站视频"""
        results = []
        
        try:
            # B站搜索API比较复杂，这里简化处理
            # 实际项目中可能需要使用B站的开放API
            results.append({
                "title": "逆水寒手游装备攻略视频",
                "url": "https://www.bilibili.com/video/BV1234567890",
                "source": "哔哩哔哩",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "type": "video"
            })
        
        except Exception as e:
            print(f"搜索B站失败: {e}")
        
        return results
    
    async def fetch_page_content(self, url: str) -> str:
        """获取页面内容"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url)
                if response.status_code == 200:
                    return response.text
        except Exception as e:
            print(f"获取页面内容失败: {e}")
        
        return ""
    
    async def extract_equipment_data(self, content: str) -> Dict[str, Any]:
        """从页面内容中提取装备数据"""
        equipment_data = {
            "equipment_mentions": [],
            "quality_mentions": [],
            "level_mentions": [],
            "meta_info": []
        }
        
        try:
            # 使用正则表达式提取装备相关信息
            
            # 提取装备品质
            quality_pattern = r'(白色|蓝色|红色|紫色|百炼|橙色)装备'
            quality_matches = re.findall(quality_pattern, content)
            equipment_data["quality_mentions"] = list(set(quality_matches))
            
            # 提取装等信息
            level_pattern = r'(\d{2,3})装等'
            level_matches = re.findall(level_pattern, content)
            equipment_data["level_mentions"] = [int(level) for level in set(level_matches)]
            
            # 提取装备名称（这个比较复杂，需要更精确的模式）
            equipment_pattern = r'【([^】]+)】'
            equipment_matches = re.findall(equipment_pattern, content)
            equipment_data["equipment_mentions"] = list(set(equipment_matches))
            
            # 提取meta相关信息
            meta_keywords = ["meta", "强势", "推荐", "最佳", "毕业", "神器"]
            for keyword in meta_keywords:
                if keyword in content:
                    # 提取包含关键词的句子
                    sentences = re.split(r'[。！？\n]', content)
                    for sentence in sentences:
                        if keyword in sentence and len(sentence) < 100:
                            equipment_data["meta_info"].append(sentence.strip())
        
        except Exception as e:
            print(f"提取装备数据失败: {e}")
        
        return equipment_data
    
    async def search_specific_equipment(self, equipment_name: str) -> Dict[str, Any]:
        """搜索特定装备信息"""
        query = f"{equipment_name} 属性 评测 攻略"
        search_results = await self.search_game_info(query, 5)
        
        equipment_info = {
            "name": equipment_name,
            "search_results": search_results,
            "extracted_data": {},
            "confidence": 0.0
        }
        
        # 分析搜索结果
        for result in search_results:
            if result.get("url"):
                content = await self.fetch_page_content(result["url"])
                if content:
                    extracted = await self.extract_equipment_data(content)
                    equipment_info["extracted_data"][result["url"]] = extracted
        
        # 计算信息可信度
        if len(search_results) > 0:
            official_count = sum(1 for r in search_results if r.get("type") == "official")
            equipment_info["confidence"] = min(0.9, 0.3 + official_count * 0.3)
        
        return equipment_info
    
    async def get_latest_version_info(self) -> Dict[str, Any]:
        """获取最新版本信息"""
        query = "版本更新 公告"
        search_results = await self.search_game_info(query, 3)
        
        version_info = {
            "current_version": "unknown",
            "update_date": "",
            "major_changes": [],
            "equipment_changes": [],
            "confidence": 0.0
        }
        
        # 分析搜索结果中的版本信息
        for result in search_results:
            if "版本" in result.get("title", "") or "更新" in result.get("title", ""):
                # 提取版本号
                version_match = re.search(r'(\d+\.\d+\.\d+)', result.get("title", ""))
                if version_match:
                    version_info["current_version"] = version_match.group(1)
                
                # 获取详细内容
                if result.get("url"):
                    content = await self.fetch_page_content(result["url"])
                    if content:
                        # 提取更新内容
                        update_content = await self.extract_equipment_data(content)
                        version_info["equipment_changes"].extend(update_content.get("meta_info", []))
                
                version_info["confidence"] = 0.8 if result.get("type") == "official" else 0.5
                break
        
        return version_info
    
    async def monitor_meta_changes(self) -> Dict[str, Any]:
        """监控meta变化"""
        queries = [
            "最强构建",
            "tier榜单", 
            "PVP meta",
            "PVE攻略"
        ]
        
        meta_changes = {
            "trending_builds": [],
            "popular_equipment": [],
            "strategy_changes": [],
            "last_monitored": datetime.now().isoformat()
        }
        
        for query in queries:
            results = await self.search_game_info(query, 3)
            
            for result in results:
                if result.get("date") == datetime.now().strftime("%Y-%m-%d"):
                    # 今天的内容，可能是新的meta变化
                    meta_changes["trending_builds"].append({
                        "title": result.get("title"),
                        "source": result.get("source"),
                        "url": result.get("url")
                    })
        
        return meta_changes


# 创建全局搜索服务实例
web_search_service = WebSearchService()
