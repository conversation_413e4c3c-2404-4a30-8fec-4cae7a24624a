#!/bin/bash

# 逆水寒智能装备助手启动脚本

echo "🚀 启动逆水寒智能装备助手..."

# 检查是否存在.env文件
if [ ! -f "backend/.env" ]; then
    echo "⚠️  未找到配置文件，正在创建..."
    cp backend/.env.example backend/.env
    echo "✅ 已创建配置文件 backend/.env，请编辑并填入您的 DeepSeek API Key"
    echo "📝 编辑命令: nano backend/.env"
    echo ""
    echo "🔑 获取 DeepSeek API Key: https://platform.deepseek.com/"
    echo ""
    read -p "按回车键继续..."
fi

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要的目录
mkdir -p backend/logs
mkdir -p backend/data

echo "🔧 正在构建和启动服务..."

# 构建并启动服务
docker-compose up --build -d

echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 检查后端健康状态
echo "🔍 检查后端服务..."
if curl -f http://localhost:8000/health &> /dev/null; then
    echo "✅ 后端服务运行正常"
else
    echo "❌ 后端服务启动失败，请检查日志"
    docker-compose logs backend
    exit 1
fi

echo ""
echo "🎉 启动完成！"
echo ""
echo "📱 前端地址: http://localhost:3000"
echo "🔧 后端API: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/docs"
echo ""
echo "📋 常用命令:"
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo ""
echo "🔧 如需修改配置，请编辑 backend/.env 文件后重启服务"
