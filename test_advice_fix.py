#!/usr/bin/env python3
"""
测试建议功能修复
"""
import asyncio
import httpx

async def test_advice_fix():
    """测试建议功能修复"""
    print("测试建议功能修复")
    print("=" * 50)
    
    # 测试建议类问题
    advice_questions = [
        "给我推荐下碎梦PVP帮会联赛的出装、内功和打造",
        "新手玩家选择什么职业比较好？",
        "逆水寒PVP怎么玩？",
        "装备强化有什么技巧？"
    ]
    
    async with httpx.AsyncClient(timeout=90.0) as client:
        
        for i, question in enumerate(advice_questions, 1):
            print(f"\n测试 {i}: {question}")
            print("-" * 40)
            
            try:
                response = await client.post(
                    "http://localhost:8000/api/v1/learning/chat",
                    json={"message": question}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        reply = data["agent_response"]
                        intent = data.get("intent_analysis", {}).get("intent", {})
                        action = data.get("intent_analysis", {}).get("action_taken", "unknown")
                        
                        print(f"意图: {intent.get('primary_intent', 'unknown')}")
                        print(f"执行动作: {action}")
                        print("Agent回复:")
                        
                        if len(reply) > 300:
                            print(f"{reply[:300]}...")
                        else:
                            print(reply)
                        
                        # 检查是否是真正的AI回复
                        if "**DeepSeek-V3" in reply:
                            print("✅ 使用了真正的AI回复")
                        else:
                            print("❌ 使用了保底回复")
                            
                        # 检查是否诚实
                        honest_indicators = [
                            "无法确定", "不确定", "无法提供准确",
                            "建议您", "查看官方", "通用建议"
                        ]
                        
                        honest_count = sum(1 for phrase in honest_indicators if phrase in reply)
                        if honest_count > 0:
                            print(f"✅ 保持诚实 ({honest_count}个诚实指标)")
                        else:
                            print("⚠️ 可能缺乏诚实性")
                            
                    else:
                        print(f"Agent失败: {data.get('error')}")
                else:
                    print(f"HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"异常: {e}")
            
            await asyncio.sleep(3)
    
    print("\n" + "=" * 50)
    print("建议功能测试完成")

async def main():
    await test_advice_fix()

if __name__ == "__main__":
    asyncio.run(main())
