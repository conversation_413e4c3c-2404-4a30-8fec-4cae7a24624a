# 开发指南

## 🛠️ 开发环境搭建

### 前置要求
- Python 3.9+
- Node.js 18+
- Docker & Docker Compose (可选)

### 本地开发

#### 1. 后端开发
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入 DeepSeek API Key

# 启动开发服务器
python main.py
```

#### 2. 前端开发
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### Docker 开发
```bash
# 一键启动所有服务
./start.sh

# 或手动启动
docker-compose up --build
```

## 📁 项目结构详解

### 后端结构
```
backend/
├── app/
│   ├── api/              # API路由
│   │   └── equipment.py  # 装备相关API
│   ├── core/             # 核心配置
│   │   └── config.py     # 应用配置
│   ├── models/           # 数据模型
│   │   └── equipment.py  # 装备模型
│   ├── services/         # 业务逻辑
│   │   ├── deepseek_service.py    # DeepSeek API服务
│   │   └── equipment_service.py   # 装备业务逻辑
│   └── utils/            # 工具函数
├── data/                 # 游戏数据
├── logs/                 # 日志文件
├── requirements.txt      # Python依赖
└── main.py              # 应用入口
```

### 前端结构
```
frontend/
├── src/
│   ├── components/       # 通用组件
│   ├── pages/           # 页面组件
│   ├── services/        # API服务
│   ├── utils/           # 工具函数
│   ├── App.tsx          # 主应用组件
│   └── main.tsx         # 应用入口
├── public/              # 静态资源
└── package.json         # 依赖配置
```

## 🔧 核心功能实现

### 1. DeepSeek API 集成
- 使用 `httpx` 异步HTTP客户端
- 支持 V3 和 R1 两个模型
- 自动错误处理和重试机制
- Token使用量监控

### 2. 装备评分算法
- 基于职业和场景的属性权重计算
- AI驱动的智能评分
- 多维度评估（攻击、防御、特殊属性）

### 3. 数据管理
- JSON文件存储装备数据
- 内存缓存提升性能
- 支持动态加载和更新

## 🧪 测试

### 后端测试
```bash
cd backend
pytest tests/ -v
```

### 前端测试
```bash
cd frontend
npm test
```

## 📊 性能优化

### 后端优化
- 异步处理提升并发性能
- 缓存机制减少API调用
- 数据库连接池
- 请求限流

### 前端优化
- 组件懒加载
- 图片压缩和CDN
- 代码分割
- 缓存策略

## 🔒 安全考虑

### API安全
- JWT认证
- 请求限流
- 输入验证
- CORS配置

### 数据安全
- 敏感信息加密
- 环境变量管理
- 日志脱敏

## 📈 监控和日志

### 日志系统
- 结构化日志记录
- 日志轮转和清理
- 错误追踪

### 性能监控
- API响应时间
- 错误率统计
- 资源使用情况

## 🚀 部署指南

### 生产环境部署
1. 配置环境变量
2. 构建Docker镜像
3. 使用Docker Compose部署
4. 配置反向代理
5. 设置SSL证书

### 扩展性考虑
- 水平扩展支持
- 负载均衡配置
- 数据库集群
- CDN集成

## 🤝 贡献指南

### 代码规范
- Python: PEP 8
- TypeScript: ESLint + Prettier
- 提交信息: Conventional Commits

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request

## 📞 技术支持

如有问题，请通过以下方式联系：
- 创建 GitHub Issue
- 发送邮件至开发团队
- 加入技术交流群
