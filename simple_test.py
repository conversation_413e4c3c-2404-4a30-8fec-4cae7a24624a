#!/usr/bin/env python3
import asyncio
import httpx

async def simple_test():
    print("简单测试Agent...")
    
    try:
        async with httpx.AsyncClient(timeout=90.0) as client:  # 增加到90秒
            response = await client.post(
                "http://localhost:8000/api/v1/learning/chat",
                json={"message": "你好"}
            )
            
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"成功: {data.get('success', False)}")
                if 'agent_response' in data:
                    print(f"回复: {data['agent_response']}")
                else:
                    print(f"数据: {data}")
            else:
                print(f"失败: {response.text}")
                
    except Exception as e:
        print(f"异常: {e}")

if __name__ == "__main__":
    asyncio.run(simple_test())
