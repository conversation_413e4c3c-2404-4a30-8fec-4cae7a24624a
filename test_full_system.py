#!/usr/bin/env python3
"""
全面系统测试 - 避免编码问题
"""
import asyncio
import httpx
import json

async def test_core_agent():
    """测试DeepSeek-V3 Agent核心功能"""
    print("测试DeepSeek-V3 Agent核心功能")
    print("=" * 50)
    
    test_cases = [
        "你好，你是什么模型？",
        "推荐一个新手职业",
        "碎梦PVP怎么玩？",
        "逆水寒有哪些装备类型？"
    ]
    
    async with httpx.AsyncClient(timeout=90.0) as client:
        for i, question in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {question}")
            print("-" * 40)
            
            try:
                response = await client.post(
                    "http://localhost:8000/api/v1/learning/chat",
                    json={"message": question}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        print("成功")
                        reply = data["agent_response"]
                        if len(reply) > 150:
                            print(f"回复: {reply[:150]}...")
                        else:
                            print(f"回复: {reply}")
                    else:
                        print("失败")
                        print(f"错误: {data.get('error', '未知错误')}")
                else:
                    print(f"HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"异常: {e}")
            
            await asyncio.sleep(2)
    
    print("\nAgent核心功能测试完成!")

async def test_social_knowledge():
    """测试社交知识库"""
    print("\n测试社交知识库系统")
    print("=" * 50)
    
    base_url = "http://localhost:8000/api/v1/social-knowledge"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # 1. 获取平台信息
        print("\n1. 获取支持的平台")
        try:
            response = await client.get(f"{base_url}/platforms")
            if response.status_code == 200:
                data = response.json()
                print("平台信息获取成功")
                for platform in data["platforms"]:
                    status = "可用" if platform["api_available"] else "需要配置"
                    print(f"  {platform['name']}: {status}")
            else:
                print(f"获取平台信息失败: {response.status_code}")
        except Exception as e:
            print(f"异常: {e}")
        
        # 2. 获取统计信息
        print("\n2. 获取知识库统计")
        try:
            response = await client.get(f"{base_url}/stats")
            if response.status_code == 200:
                data = response.json()
                stats = data["stats"]
                print("统计信息获取成功")
                print(f"  总知识条目: {stats['total_knowledge_items']}")
                print(f"  缓存状态: {stats['cache_status']}")
                if stats["platform_breakdown"]:
                    print("  平台分布:")
                    for platform, count in stats["platform_breakdown"].items():
                        print(f"    {platform}: {count}条")
            else:
                print(f"获取统计信息失败: {response.status_code}")
        except Exception as e:
            print(f"异常: {e}")
        
        # 3. 获取热门知识
        print("\n3. 获取热门知识")
        try:
            response = await client.get(f"{base_url}/trending?limit=3")
            if response.status_code == 200:
                data = response.json()
                print("热门知识获取成功")
                print(f"  找到 {data['returned_count']} 条热门内容")
                
                for i, item in enumerate(data["trending_knowledge"], 1):
                    print(f"  {i}. {item['title']}")
                    print(f"     平台: {item['platform']}")
                    print(f"     热度: {item['engagement']['likes']} 赞")
            else:
                print(f"获取热门知识失败: {response.status_code}")
        except Exception as e:
            print(f"异常: {e}")
        
        # 4. 搜索测试
        print("\n4. 搜索知识")
        try:
            response = await client.post(
                f"{base_url}/search",
                json={"query": "职业选择", "limit": 2}
            )
            if response.status_code == 200:
                data = response.json()
                print(f"搜索'职业选择'成功")
                print(f"  找到 {data['total_found']} 条相关内容")
                
                for i, item in enumerate(data["search_results"], 1):
                    print(f"  {i}. {item['title']} ({item['platform']})")
            else:
                print(f"搜索失败: {response.status_code}")
        except Exception as e:
            print(f"异常: {e}")
    
    print("\n社交知识库测试完成!")

async def test_performance():
    """测试性能"""
    print("\n测试系统性能")
    print("=" * 50)
    
    import time
    
    async with httpx.AsyncClient(timeout=90.0) as client:
        
        # 单个请求性能测试
        print("\n1. 单个请求性能测试")
        start_time = time.time()
        
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/learning/chat",
                json={"message": "你好"}
            )
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print(f"  成功! 耗时: {elapsed_time:.2f}秒")
                    if elapsed_time < 15:
                        print("  性能: 优秀")
                    elif elapsed_time < 30:
                        print("  性能: 良好")
                    else:
                        print("  性能: 需要优化")
                else:
                    print(f"  失败: {data.get('error')}")
            else:
                print(f"  HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  异常: {e}")
        
        # 并发请求测试
        print("\n2. 并发请求测试")
        
        async def single_request(request_id):
            try:
                start_time = time.time()
                response = await client.post(
                    "http://localhost:8000/api/v1/learning/chat",
                    json={"message": f"测试请求{request_id}"}
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    data = response.json()
                    success = data.get("success", False)
                    return {
                        "id": request_id,
                        "success": success,
                        "time": end_time - start_time
                    }
                else:
                    return {"id": request_id, "success": False, "time": end_time - start_time}
            except Exception as e:
                return {"id": request_id, "success": False, "error": str(e)}
        
        # 发送3个并发请求
        tasks = [single_request(i) for i in range(1, 4)]
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        print(f"  总耗时: {total_time:.2f}秒")
        
        success_count = 0
        for result in results:
            if isinstance(result, dict) and result.get("success"):
                success_count += 1
                print(f"  请求{result['id']}: 成功 ({result['time']:.2f}秒)")
            elif isinstance(result, dict):
                print(f"  请求{result['id']}: 失败")
            else:
                print(f"  请求异常: {result}")
        
        print(f"  成功率: {success_count}/3 ({success_count/3*100:.1f}%)")
    
    print("\n性能测试完成!")

async def main():
    """主测试函数"""
    print("全面系统测试开始")
    print("=" * 60)
    
    # 1. 测试Agent核心功能
    await test_core_agent()
    
    # 2. 测试社交知识库
    await test_social_knowledge()
    
    # 3. 测试性能
    await test_performance()
    
    print("\n" + "=" * 60)
    print("全面系统测试完成!")
    
    print("\n总结:")
    print("1. DeepSeek-V3 Agent: 核心AI功能")
    print("2. 社交知识库: 小红书、抖音、B站数据")
    print("3. 性能优化: 超时配置和并发支持")
    print("4. 完整API: REST接口和前端界面")
    
    print("\n系统已准备就绪，可以投入使用!")

if __name__ == "__main__":
    asyncio.run(main())
