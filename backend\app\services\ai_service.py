"""
AI服务 - 负责与DeepSeek API交互
真实产品级实现
"""
import asyncio
import httpx
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger

from app.core.config import get_settings

settings = get_settings()


class AIService:
    """AI分析服务"""
    
    def __init__(self):
        self.api_key = "sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk"
        self.base_url = "https://api.siliconflow.cn/v1"
        self.model = "Pro/deepseek-ai/DeepSeek-V3"  # 使用用户指定的DeepSeek-V3模型
        self.timeout = 60.0  # 增加到60秒，给DeepSeek-V3更多时间
        self.max_retries = 3
        self.available = False
        
    async def initialize(self):
        """初始化AI服务"""
        logger.info("初始化AI服务...")
        
        # 测试API连接
        try:
            test_result = await self._test_connection()
            if test_result:
                self.available = True
                logger.info("AI服务初始化成功")
            else:
                logger.warning("AI服务连接测试失败，将使用降级模式")
        except Exception as e:
            logger.error(f"AI服务初始化失败: {e}")
            
    async def cleanup(self):
        """清理资源"""
        logger.info("清理AI服务资源...")
        
    def is_available(self) -> bool:
        """检查AI服务是否可用"""
        return self.available
        
    async def _test_connection(self) -> bool:
        """测试API连接"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:  # 连接测试也增加超时
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }

                payload = {
                    "model": self.model,
                    "messages": [
                        {"role": "user", "content": "测试连接"}
                    ],
                    "max_tokens": 10,
                    "temperature": 0.3
                }

                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )

                if response.status_code == 200:
                    logger.info("AI连接测试成功")
                    return True
                else:
                    logger.warning(f"AI连接测试失败，状态码: {response.status_code}")
                    return False

        except Exception as e:
            logger.error(f"AI连接测试失败: {e}")
            return False
    
    async def analyze_equipment(self, equipment_name: str, search_context: Dict = None) -> str:
        """分析装备"""
        if not self.available:
            return self._get_fallback_analysis(equipment_name)
            
        prompt = self._build_equipment_analysis_prompt(equipment_name, search_context)
        
        try:
            response = await self._make_request(prompt, "equipment_analysis")
            return response if response else self._get_fallback_analysis(equipment_name)
            
        except Exception as e:
            logger.error(f"装备分析失败: {e}")
            return self._get_fallback_analysis(equipment_name)
    
    async def analyze_meta_trends(self, search_results: List[Dict]) -> Dict[str, Any]:
        """分析meta趋势"""
        if not self.available:
            return self._get_fallback_meta()
            
        prompt = self._build_meta_analysis_prompt(search_results)
        
        try:
            response = await self._make_request(prompt, "meta_analysis")
            if response:
                return self._parse_meta_response(response)
            else:
                return self._get_fallback_meta()
                
        except Exception as e:
            logger.error(f"Meta分析失败: {e}")
            return self._get_fallback_meta()
    
    async def extract_version_info(self, content: str) -> Dict[str, Any]:
        """从内容中提取版本信息"""
        if not self.available:
            return self._get_fallback_version()
            
        prompt = self._build_version_extraction_prompt(content)
        
        try:
            response = await self._make_request(prompt, "version_extraction")
            if response:
                return self._parse_version_response(response)
            else:
                return self._get_fallback_version()
                
        except Exception as e:
            logger.error(f"版本信息提取失败: {e}")
            return self._get_fallback_version()
    
    async def _make_request(self, prompt: str, task_type: str) -> Optional[str]:
        """发送AI请求"""
        logger.info(f"开始AI请求 - 模型: {self.model}, 任务: {task_type}")

        for attempt in range(self.max_retries):
            try:
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    }

                    payload = {
                        "model": self.model,
                        "messages": [
                            {
                                "role": "system",
                                "content": self._get_system_prompt(task_type)
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        "temperature": 0.3,
                        "max_tokens": 1500
                    }

                    logger.info(f"发送请求到: {self.base_url}/chat/completions")
                    response = await client.post(
                        f"{self.base_url}/chat/completions",
                        headers=headers,
                        json=payload
                    )

                    logger.info(f"收到响应: {response.status_code}")

                    if response.status_code == 200:
                        result = response.json()
                        content = result["choices"][0]["message"]["content"]
                        logger.info(f"AI回复成功: {content[:100]}...")
                        return content
                    else:
                        logger.warning(f"AI请求失败 (尝试 {attempt + 1}/{self.max_retries}): {response.status_code}")
                        logger.warning(f"错误详情: {response.text}")
                        if attempt == self.max_retries - 1:
                            logger.error(f"AI请求最终失败: {response.text}")

            except Exception as e:
                logger.warning(f"AI请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)  # 重试前等待

        logger.error("所有AI请求尝试都失败了")
        return None
    
    def _get_system_prompt(self, task_type: str) -> str:
        """获取系统提示词"""
        prompts = {
            "equipment_analysis": "你是逆水寒手游的专业装备分析师，熟悉游戏的装备系统、职业特点和当前meta。请提供专业、准确的装备分析。",
            "meta_analysis": "你是逆水寒手游的meta分析专家，能够从搜索结果中提取关键信息，分析当前游戏趋势。",
            "version_extraction": "你是逆水寒手游的版本信息专家，能够从网页内容中准确提取版本号、更新内容等关键信息。",
            "intent_analysis": """你是逆水寒手游的智能意图分析专家。请分析用户消息并返回JSON格式结果。

JSON格式要求：
{
  "intent": "用户意图类型",
  "game_level": "用户游戏水平(新手/进阶/高级/未知)",
  "focus": "关注领域(PVP/PVE/装备/职业/攻略/其他)",
  "urgency": "紧急程度(一般咨询/急需帮助/深度讨论)"
}

意图类型包括：寻求游戏建议、装备咨询、职业选择、PVP技巧、PVE攻略、其他需求等。""",
            "general": """你是逆水寒手游的专业AI助手。回答要求：
1. 直接回答核心问题，不要过多寒暄
2. 优先提供具体的游戏内容（装备、技能、数值等）
3. 如果有社交媒体数据，重点引用其中的具体信息
4. 避免冗长的背景介绍和免责声明
5. 回答要简洁有力，重点突出"""
        }
        return prompts.get(task_type, "你是逆水寒手游的专业分析师。")
    
    def _build_equipment_analysis_prompt(self, equipment_name: str, search_context: Dict = None) -> str:
        """构建装备分析提示词"""
        context_info = ""
        if search_context and search_context.get('results'):
            context_info = f"\n搜索到的相关信息:\n{json.dumps(search_context, ensure_ascii=False, indent=2)}"
            
        return f"""
请分析逆水寒手游装备"{equipment_name}"。
{context_info}

请提供以下分析:
1. 装备基本信息 (品质、等级、类型)
2. 属性特点和优势
3. 适合的职业和场景
4. 在当前版本中的地位
5. 获取途径和使用建议
6. 与同类装备的对比

请基于最新的游戏信息进行分析，如果信息不足请明确说明。
"""
    
    def _build_meta_analysis_prompt(self, search_results: List[Dict]) -> str:
        """构建meta分析提示词"""
        results_text = json.dumps(search_results, ensure_ascii=False, indent=2)
        
        return f"""
基于以下搜索结果，分析逆水寒手游当前的meta趋势:

搜索结果:
{results_text}

请以JSON格式返回分析结果，包含:
{{
    "trending_builds": ["流派1", "流派2"],
    "popular_equipment": ["装备1", "装备2"],
    "strategy_changes": ["变化1", "变化2"],
    "confidence": 0.8
}}
"""
    
    def _build_version_extraction_prompt(self, content: str) -> str:
        """构建版本提取提示词"""
        # 限制内容长度避免token超限
        limited_content = content[:2000] if len(content) > 2000 else content
        
        return f"""
从以下网页内容中提取逆水寒手游的版本信息:

内容:
{limited_content}

请以JSON格式返回:
{{
    "current_version": "版本号",
    "update_date": "更新日期",
    "major_changes": ["主要变化1", "主要变化2"],
    "equipment_changes": ["装备变化1", "装备变化2"],
    "confidence": 0.9
}}
"""
    
    def _parse_meta_response(self, response: str) -> Dict[str, Any]:
        """解析meta分析响应"""
        try:
            # 尝试提取JSON
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
        except Exception as e:
            logger.error(f"解析meta响应失败: {e}")
            
        return self._get_fallback_meta()
    
    def _parse_version_response(self, response: str) -> Dict[str, Any]:
        """解析版本信息响应"""
        try:
            # 尝试提取JSON
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
        except Exception as e:
            logger.error(f"解析版本响应失败: {e}")
            
        return self._get_fallback_version()
    
    def _get_fallback_analysis(self, equipment_name: str) -> str:
        """获取降级分析"""
        return f"""
{equipment_name} - 基础分析

由于AI服务暂时不可用，提供基础分析：

1. 装备类型：根据名称判断，可能是{self._guess_equipment_type(equipment_name)}
2. 建议：查看官方攻略获取详细属性信息
3. 获取途径：通常通过副本、商店或活动获得
4. 使用建议：根据职业特点选择合适的强化方向

请访问官方网站或社区论坛获取最新的装备信息。
"""
    
    def _get_fallback_meta(self) -> Dict[str, Any]:
        """获取降级meta信息"""
        return {
            "trending_builds": ["请查看官方攻略", "关注社区讨论"],
            "popular_equipment": ["百炼装备", "高等级装备"],
            "strategy_changes": ["请关注版本更新", "查看官方公告"],
            "confidence": 0.3,
            "note": "AI服务暂时不可用，建议查看官方信息"
        }
    
    def _get_fallback_version(self) -> Dict[str, Any]:
        """获取降级版本信息"""
        return {
            "current_version": "请查看官网",
            "update_date": datetime.now().strftime("%Y-%m-%d"),
            "major_changes": ["请访问官方网站获取最新信息"],
            "equipment_changes": ["关注官方更新公告"],
            "confidence": 0.2,
            "note": "AI服务暂时不可用"
        }
    
    def _guess_equipment_type(self, equipment_name: str) -> str:
        """根据名称猜测装备类型"""
        if "武器" in equipment_name or "剑" in equipment_name or "刀" in equipment_name:
            return "武器"
        elif "护甲" in equipment_name or "衣" in equipment_name:
            return "防具"
        elif "戒指" in equipment_name or "项链" in equipment_name:
            return "饰品"
        else:
            return "装备"


# 创建全局AI服务实例
ai_service = AIService()
