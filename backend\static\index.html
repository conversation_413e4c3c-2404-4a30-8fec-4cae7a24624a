<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逆水寒智能装备助手 - 学习Agent演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 4px solid #007bff;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .feature-card p {
            color: #6c757d;
            line-height: 1.6;
        }
        
        .demo-section {
            background: #fff;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .demo-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .demo-section h3::before {
            content: "🤖";
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .api-demo {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            border-left: 3px solid #28a745;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #28a745;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .loading {
            text-align: center;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 逆水寒智能装备助手</h1>
            <p>基于AI的自主学习Agent - 实时获取最新游戏信息</p>
            <div style="margin-top: 15px;">
                <span class="status-indicator"></span>
                <span>学习Agent运行中</span>
            </div>
        </div>
        
        <div class="content">
            <!-- 功能特性 -->
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🌐 实时信息搜索</h3>
                    <p>自动从官网、论坛、视频等多个渠道搜索最新的逆水寒游戏信息，确保数据的时效性和准确性。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🧠 AI智能分析</h3>
                    <p>使用DeepSeek R1/V3模型深度理解游戏内容，提供专业级的装备分析和策略建议。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🔄 自动知识更新</h3>
                    <p>定期自动更新知识库，学习最新的游戏变化和meta趋势，持续优化分析准确性。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🎯 个性化推荐</h3>
                    <p>基于职业、场景和用户反馈，提供精准的装备推荐和搭配建议。</p>
                </div>
            </div>
            
            <!-- Agent状态演示 -->
            <div class="demo-section">
                <h3>Agent状态监控</h3>
                <button class="btn" onclick="checkAgentStatus()">检查Agent状态</button>
                <div id="agentStatus" class="result-box" style="display: none;"></div>
            </div>
            
            <!-- 搜索演示 -->
            <div class="demo-section">
                <h3>实时信息搜索</h3>
                <input type="text" id="searchQuery" placeholder="输入搜索关键词（如：装备推荐、meta构建）" 
                       style="width: 70%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                <button class="btn" onclick="searchGameInfo()">搜索</button>
                <div id="searchResults" class="result-box" style="display: none;"></div>
            </div>
            
            <!-- 装备分析演示 -->
            <div class="demo-section">
                <h3>装备实时分析</h3>
                <input type="text" id="equipmentName" placeholder="输入装备名称（如：百炼武器、海珠装备）" 
                       style="width: 70%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                <button class="btn" onclick="analyzeEquipment()">分析装备</button>
                <div id="equipmentAnalysis" class="result-box" style="display: none;"></div>
            </div>
            
            <!-- 版本信息演示 -->
            <div class="demo-section">
                <h3>游戏版本信息</h3>
                <button class="btn" onclick="getVersionInfo()">获取最新版本信息</button>
                <div id="versionInfo" class="result-box" style="display: none;"></div>
            </div>
            
            <!-- Meta信息演示 -->
            <div class="demo-section">
                <h3>当前Meta分析</h3>
                <button class="btn" onclick="getMetaInfo()">获取Meta信息</button>
                <div id="metaInfo" class="result-box" style="display: none;"></div>
            </div>
            
            <!-- API文档链接 -->
            <div class="demo-section">
                <h3>开发者资源</h3>
                <p>完整的API文档和更多功能请访问：</p>
                <a href="/docs" target="_blank" class="btn">API文档</a>
                <a href="https://github.com/your-repo/nsh-agent" target="_blank" class="btn">GitHub仓库</a>
            </div>
        </div>
    </div>

    <script>
        // 检查Agent状态
        async function checkAgentStatus() {
            const resultDiv = document.getElementById('agentStatus');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">正在检查Agent状态...</div>';
            
            try {
                const response = await fetch('/api/v1/learning/status');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h4>🤖 Agent状态</h4>
                    <p><strong>状态:</strong> ${data.agent_status}</p>
                    <p><strong>知识库大小:</strong> ${data.knowledge_base_size}</p>
                    <p><strong>更新次数:</strong> ${data.update_count}</p>
                    <p><strong>最后更新:</strong> ${data.last_update}</p>
                    <p><strong>功能特性:</strong></p>
                    <ul>
                        ${data.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">错误: ${error.message}</p>`;
            }
        }
        
        // 搜索游戏信息
        async function searchGameInfo() {
            const query = document.getElementById('searchQuery').value;
            const resultDiv = document.getElementById('searchResults');
            
            if (!query) {
                alert('请输入搜索关键词');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">正在搜索...</div>';
            
            try {
                const response = await fetch(`/api/v1/learning/search?query=${encodeURIComponent(query)}`);
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h4>🔍 搜索结果 (${data.count}条)</h4>
                    ${data.results.map(result => `
                        <div style="margin: 10px 0; padding: 10px; border-left: 3px solid #007bff;">
                            <strong>${result.title}</strong><br>
                            <small>来源: ${result.source} | 日期: ${result.date}</small><br>
                            <a href="${result.url}" target="_blank">查看详情</a>
                        </div>
                    `).join('')}
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">搜索失败: ${error.message}</p>`;
            }
        }
        
        // 分析装备
        async function analyzeEquipment() {
            const equipmentName = document.getElementById('equipmentName').value;
            const resultDiv = document.getElementById('equipmentAnalysis');
            
            if (!equipmentName) {
                alert('请输入装备名称');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">正在分析装备...</div>';
            
            try {
                const response = await fetch(`/api/v1/learning/equipment/${encodeURIComponent(equipmentName)}`);
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h4>⚔️ ${data.equipment_name} 分析结果</h4>
                    <p><strong>信息可信度:</strong> ${(data.search_info.confidence * 100).toFixed(1)}%</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                        <h5>🧠 AI分析:</h5>
                        <pre style="white-space: pre-wrap; font-family: inherit;">${data.ai_analysis}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">分析失败: ${error.message}</p>`;
            }
        }
        
        // 获取版本信息
        async function getVersionInfo() {
            const resultDiv = document.getElementById('versionInfo');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">正在获取版本信息...</div>';
            
            try {
                const response = await fetch('/api/v1/learning/version');
                const data = await response.json();
                const versionInfo = data.version_info;
                
                resultDiv.innerHTML = `
                    <h4>📱 游戏版本信息</h4>
                    <p><strong>当前版本:</strong> ${versionInfo.current_version}</p>
                    <p><strong>更新日期:</strong> ${versionInfo.update_date}</p>
                    <p><strong>主要变化:</strong></p>
                    <ul>
                        ${versionInfo.major_changes.map(change => `<li>${change}</li>`).join('')}
                    </ul>
                    <p><strong>装备变化:</strong></p>
                    <ul>
                        ${versionInfo.equipment_changes.map(change => `<li>${change}</li>`).join('')}
                    </ul>
                    <p><strong>信息可信度:</strong> ${(versionInfo.confidence * 100).toFixed(1)}%</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">获取失败: ${error.message}</p>`;
            }
        }
        
        // 获取Meta信息
        async function getMetaInfo() {
            const resultDiv = document.getElementById('metaInfo');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">正在获取Meta信息...</div>';
            
            try {
                const response = await fetch('/api/v1/learning/meta');
                const data = await response.json();
                const metaInfo = data.meta_info;
                
                resultDiv.innerHTML = `
                    <h4>📊 当前Meta分析</h4>
                    <h5>🔥 热门构建:</h5>
                    ${metaInfo.trending_builds.map(build => `
                        <div style="margin: 5px 0;">
                            <a href="${build.url}" target="_blank">${build.title}</a>
                            <small> - ${build.source}</small>
                        </div>
                    `).join('')}
                    
                    <h5>⚔️ 热门装备:</h5>
                    <ul>
                        ${metaInfo.popular_equipment.map(equipment => `<li>${equipment}</li>`).join('')}
                    </ul>
                    
                    <h5>🎯 策略变化:</h5>
                    <ul>
                        ${metaInfo.strategy_changes.map(change => `<li>${change}</li>`).join('')}
                    </ul>
                    
                    <p><strong>最后监控时间:</strong> ${metaInfo.last_monitored}</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">获取失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动检查Agent状态
        window.onload = function() {
            checkAgentStatus();
        };
    </script>
</body>
</html>
