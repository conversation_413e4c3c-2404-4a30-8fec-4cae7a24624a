"""
学习服务 - 整合AI和搜索服务，提供智能学习能力
真实产品级实现
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
from loguru import logger

from app.services.ai_service import AIService
from app.services.search_service import SearchService


class LearningService:
    """智能学习服务"""
    
    def __init__(self, ai_service: AIService, search_service: SearchService):
        self.ai_service = ai_service
        self.search_service = search_service
        self.knowledge_base = {}
        self.last_update = None
        self.update_interval = timedelta(hours=6)  # 6小时更新一次
        self.available = False
        
        # 知识库文件路径
        self.knowledge_file = Path("data/knowledge_base.json")
        self.knowledge_file.parent.mkdir(exist_ok=True)
        
    async def initialize(self):
        """初始化学习服务"""
        logger.info("初始化学习服务...")
        
        # 加载知识库
        await self._load_knowledge_base()
        
        # 检查服务可用性
        ai_available = self.ai_service.is_available()
        search_available = self.search_service.is_available()
        
        if ai_available or search_available:
            self.available = True
            logger.info(f"✅ 学习服务初始化成功 (AI: {ai_available}, 搜索: {search_available})")
        else:
            logger.warning("⚠️ 学习服务部分功能不可用，将使用降级模式")
            
        # 检查是否需要更新
        if await self._should_update():
            logger.info("检测到需要更新知识库，启动后台更新...")
            asyncio.create_task(self._background_update())
            
    async def cleanup(self):
        """清理资源"""
        logger.info("清理学习服务资源...")
        await self._save_knowledge_base()
        
    def is_available(self) -> bool:
        """检查学习服务是否可用"""
        return self.available
        
    def get_current_time(self) -> str:
        """获取当前时间"""
        return datetime.now().isoformat()
    
    async def get_status(self) -> Dict[str, Any]:
        """获取学习状态"""
        return {
            "agent_status": "active" if self.available else "limited",
            "last_update": self.last_update.isoformat() if self.last_update else None,
            "knowledge_base_size": len(self.knowledge_base),
            "update_count": self.knowledge_base.get("update_count", 0),
            "should_update": await self._should_update(),
            "services": {
                "ai_service": self.ai_service.is_available(),
                "search_service": self.search_service.is_available()
            },
            "features": [
                "实时信息搜索",
                "AI智能分析",
                "自动知识更新",
                "多源信息验证"
            ]
        }
    
    async def analyze_equipment(self, equipment_name: str) -> Dict[str, Any]:
        """分析装备 - 使用内置数据库"""
        logger.info(f"开始分析装备: {equipment_name}")

        from app.data.equipment_database import equipment_db

        # 1. 从装备数据库搜索
        equipment_results = equipment_db.search_equipment(equipment_name)

        if equipment_results:
            # 找到匹配的装备
            equipment = equipment_results[0]  # 取最匹配的
            analysis = equipment_db.analyze_equipment(equipment)

            # 生成详细分析
            detailed_analysis = self._generate_detailed_analysis(equipment, analysis)

            # 更新知识库
            await self._update_equipment_knowledge(equipment_name, equipment, analysis)

            return {
                "equipment_name": equipment_name,
                "found": True,
                "equipment_data": {
                    "id": equipment.id,
                    "name": equipment.name,
                    "quality": equipment.quality.value,
                    "type": equipment.type.value,
                    "level": equipment.level,
                    "attributes": [{"name": attr.name, "value": attr.value, "percentage": attr.percentage}
                                 for attr in equipment.attributes],
                    "suitable_professions": [p.value for p in equipment.suitable_professions],
                    "description": equipment.description,
                    "obtain_method": equipment.obtain_method,
                    "special_effects": equipment.special_effects,
                    "set_name": equipment.set_name
                },
                "analysis": analysis,
                "detailed_analysis": detailed_analysis,
                "timestamp": self.get_current_time(),
                "analysis_quality": "high"
            }
        else:
            # 没找到装备，提供建议
            suggestions = await self._get_equipment_suggestions(equipment_name)

            return {
                "equipment_name": equipment_name,
                "found": False,
                "message": f"未找到名为'{equipment_name}'的装备",
                "suggestions": suggestions,
                "timestamp": self.get_current_time(),
                "analysis_quality": "low"
            }
    
    async def trigger_update(self) -> Dict[str, Any]:
        """触发学习更新"""
        logger.info("手动触发知识库更新...")
        
        try:
            update_result = await self._perform_update()
            return {
                "message": "知识库更新完成",
                "status": "success",
                "update_result": update_result,
                "timestamp": self.get_current_time()
            }
        except Exception as e:
            logger.error(f"手动更新失败: {e}")
            return {
                "message": "知识库更新失败",
                "status": "error",
                "error": str(e),
                "timestamp": self.get_current_time()
            }
    
    async def _should_update(self) -> bool:
        """判断是否需要更新"""
        if not self.last_update:
            return True
        return datetime.now() - self.last_update > self.update_interval
    
    async def _background_update(self):
        """后台更新任务"""
        try:
            await self._perform_update()
        except Exception as e:
            logger.error(f"后台更新失败: {e}")
    
    async def _perform_update(self) -> Dict[str, Any]:
        """执行更新"""
        logger.info("开始执行知识库更新...")
        
        update_result = {
            "version_info": None,
            "meta_info": None,
            "search_tests": [],
            "update_time": self.get_current_time()
        }
        
        try:
            # 1. 更新版本信息
            if self.search_service.is_available():
                version_info = await self.search_service.get_version_info()
                update_result["version_info"] = version_info
                self.knowledge_base["latest_version"] = version_info
                
            # 2. 更新meta信息
            if self.search_service.is_available():
                meta_info = await self.search_service.get_meta_info()
                update_result["meta_info"] = meta_info
                self.knowledge_base["latest_meta"] = meta_info
                
            # 3. 测试搜索功能
            test_queries = ["装备推荐", "版本更新", "meta构建"]
            for query in test_queries:
                try:
                    results = await self.search_service.search_game_info(query, 3)
                    update_result["search_tests"].append({
                        "query": query,
                        "results_count": len(results),
                        "success": True
                    })
                except Exception as e:
                    update_result["search_tests"].append({
                        "query": query,
                        "error": str(e),
                        "success": False
                    })
            
            # 4. 更新统计信息
            self.knowledge_base["update_count"] = self.knowledge_base.get("update_count", 0) + 1
            self.knowledge_base["last_update"] = self.get_current_time()
            self.last_update = datetime.now()
            
            # 5. 保存知识库
            await self._save_knowledge_base()
            
            logger.info("知识库更新完成")
            return update_result
            
        except Exception as e:
            logger.error(f"执行更新失败: {e}")
            raise
    
    async def _load_knowledge_base(self):
        """加载知识库"""
        try:
            if self.knowledge_file.exists():
                with open(self.knowledge_file, 'r', encoding='utf-8') as f:
                    self.knowledge_base = json.load(f)
                
                # 解析最后更新时间
                if "last_update" in self.knowledge_base:
                    self.last_update = datetime.fromisoformat(self.knowledge_base["last_update"])
                    
                logger.info(f"知识库加载成功，包含 {len(self.knowledge_base)} 个条目")
            else:
                logger.info("知识库文件不存在，将创建新的知识库")
                self.knowledge_base = {
                    "created_at": self.get_current_time(),
                    "update_count": 0,
                    "equipment_analyses": {},
                    "search_history": []
                }
        except Exception as e:
            logger.error(f"加载知识库失败: {e}")
            self.knowledge_base = {}
    
    async def _save_knowledge_base(self):
        """保存知识库"""
        try:
            with open(self.knowledge_file, 'w', encoding='utf-8') as f:
                json.dump(self.knowledge_base, f, ensure_ascii=False, indent=2)
            logger.debug("知识库保存成功")
        except Exception as e:
            logger.error(f"保存知识库失败: {e}")
    
    async def _update_equipment_knowledge(self, equipment_name: str, search_info: Dict, ai_analysis: str):
        """更新装备知识"""
        if "equipment_analyses" not in self.knowledge_base:
            self.knowledge_base["equipment_analyses"] = {}
            
        self.knowledge_base["equipment_analyses"][equipment_name] = {
            "search_info": search_info,
            "ai_analysis": ai_analysis,
            "analyzed_at": self.get_current_time(),
            "analysis_count": self.knowledge_base["equipment_analyses"].get(equipment_name, {}).get("analysis_count", 0) + 1
        }
        
        # 异步保存
        asyncio.create_task(self._save_knowledge_base())
    
    def _calculate_search_confidence(self, search_results: List[Dict]) -> float:
        """计算搜索结果可信度"""
        if not search_results:
            return 0.0
            
        total_confidence = sum(result.get('confidence', 0.5) for result in search_results)
        avg_confidence = total_confidence / len(search_results)
        
        # 根据结果数量调整
        count_factor = min(1.0, len(search_results) / 5)
        
        return min(0.95, avg_confidence * count_factor)

    def _generate_detailed_analysis(self, equipment, analysis: Dict) -> str:
        """生成详细分析"""
        from app.data.equipment_database import EquipmentQuality

        analysis_text = f"""
🔍 {equipment.name} 详细分析

📊 基础信息:
• 品质: {equipment.quality.value}
• 类型: {equipment.type.value}
• 等级: {equipment.level}
• 评分: {analysis['score']}/100 ({analysis['rating']}级)

⚔️ 属性详情:
"""

        for attr in equipment.attributes:
            symbol = "%" if attr.percentage else ""
            analysis_text += f"• {attr.name}: {attr.value}{symbol}\n"

        analysis_text += f"""
🎯 适合职业: {', '.join([p.value for p in equipment.suitable_professions])}

✨ 特殊效果:
"""
        for effect in equipment.special_effects:
            analysis_text += f"• {effect}\n"

        analysis_text += f"""
💪 优势:
"""
        for strength in analysis['strengths']:
            analysis_text += f"• {strength}\n"

        if analysis['weaknesses']:
            analysis_text += f"""
⚠️ 劣势:
"""
            for weakness in analysis['weaknesses']:
                analysis_text += f"• {weakness}\n"

        analysis_text += f"""
💡 使用建议:
"""
        for rec in analysis['recommendations']:
            analysis_text += f"• {rec}\n"

        analysis_text += f"""
📍 获取途径: {equipment.obtain_method}
"""

        if equipment.set_name:
            analysis_text += f"🎭 套装: {equipment.set_name}"

        return analysis_text.strip()

    async def _get_equipment_suggestions(self, equipment_name: str) -> List[Dict]:
        """获取装备建议"""
        from app.data.equipment_database import equipment_db

        suggestions = []

        # 模糊匹配
        all_equipment = equipment_db.get_all_equipment()

        for equipment in all_equipment:
            # 检查名称相似度
            if any(word in equipment.name for word in equipment_name.split()):
                suggestions.append({
                    "name": equipment.name,
                    "quality": equipment.quality.value,
                    "level": equipment.level,
                    "reason": "名称相似"
                })

        # 如果没有相似的，推荐热门装备
        if not suggestions:
            popular_equipment = [
                equipment_db.get_equipment_by_id("bailian_sword_001"),
                equipment_db.get_equipment_by_id("chaoguang_staff_001"),
                equipment_db.get_equipment_by_id("duzhen_necklace_001")
            ]

            for eq in popular_equipment:
                if eq:
                    suggestions.append({
                        "name": eq.name,
                        "quality": eq.quality.value,
                        "level": eq.level,
                        "reason": "热门推荐"
                    })

        return suggestions[:5]

    async def _update_equipment_knowledge(self, equipment_name: str, equipment, analysis: Dict):
        """更新装备知识"""
        if "equipment_analyses" not in self.knowledge_base:
            self.knowledge_base["equipment_analyses"] = {}

        self.knowledge_base["equipment_analyses"][equipment_name] = {
            "equipment_data": {
                "id": equipment.id,
                "name": equipment.name,
                "quality": equipment.quality.value,
                "level": equipment.level
            },
            "analysis": analysis,
            "analyzed_at": self.get_current_time(),
            "analysis_count": self.knowledge_base["equipment_analyses"].get(equipment_name, {}).get("analysis_count", 0) + 1
        }

        # 异步保存
        asyncio.create_task(self._save_knowledge_base())

    def _assess_analysis_quality(self, search_info: Dict, ai_analysis: str) -> str:
        """评估分析质量"""
        search_confidence = search_info.get('confidence', 0)
        ai_available = "AI分析暂时不可用" not in ai_analysis
        
        if search_confidence > 0.7 and ai_available:
            return "high"
        elif search_confidence > 0.4 or ai_available:
            return "medium"
        else:
            return "low"
