#!/usr/bin/env python3
"""
直接测试DeepSeek-V3 API连接
"""
import asyncio
import httpx
import json

async def test_deepseek_v3_direct():
    """直接测试DeepSeek-V3 API"""
    print("测试DeepSeek-V3 API连接...")
    
    api_key = "sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk"
    base_url = "https://api.siliconflow.cn/v1"
    model = "Pro/deepseek-ai/DeepSeek-V3"
    
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": model,
                "messages": [
                    {"role": "user", "content": "你好，请简单回复确认你是DeepSeek-V3模型"}
                ],
                "max_tokens": 100,
                "temperature": 0.7
            }
            
            print(f"请求模型: {model}")
            response = await client.post(
                f"{base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                print(f"成功! 回复: {content}")
                return True
            else:
                print(f"失败: {response.text}")
                return False
                
    except Exception as e:
        print(f"异常: {e}")
        return False

async def test_agent_chat():
    """测试Agent聊天"""
    print("\n测试Agent聊天...")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                "http://localhost:8000/api/v1/learning/chat",
                json={"message": "你好，你是什么模型？"}
            )
            
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"成功: {data['success']}")
                if data["success"]:
                    print(f"回复: {data['agent_response'][:200]}...")
                else:
                    print(f"降级回复: {data['fallback_response'][:200]}...")
            else:
                print(f"失败: {response.text}")
                
    except Exception as e:
        print(f"异常: {e}")

async def main():
    print("=" * 50)
    print("DeepSeek-V3 连接测试")
    print("=" * 50)
    
    # 1. 直接测试API
    success = await test_deepseek_v3_direct()
    print(f"直接API测试结果: {'成功' if success else '失败'}")
    
    # 2. 测试Agent
    await test_agent_chat()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(main())
