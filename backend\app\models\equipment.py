"""
装备数据模型
"""
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union
from enum import Enum


class EquipmentType(str, Enum):
    """装备类型枚举"""
    WEAPON = "weapon"          # 武器
    ARMOR = "armor"           # 护甲
    ACCESSORY = "accessory"   # 饰品
    RING = "ring"             # 戒指
    NECKLACE = "necklace"     # 项链
    BRACELET = "bracelet"     # 手镯


class AttributeType(str, Enum):
    """属性类型枚举"""
    # 基础属性
    ATTACK = "attack"                    # 攻击力
    DEFENSE = "defense"                  # 防御力
    HP = "hp"                           # 生命值
    MP = "mp"                           # 内力值
    
    # 战斗属性
    CRIT_RATE = "crit_rate"             # 暴击率
    CRIT_DAMAGE = "crit_damage"         # 暴击伤害
    HIT_RATE = "hit_rate"               # 命中率
    DODGE_RATE = "dodge_rate"           # 闪避率
    
    # 抗性属性
    FIRE_RESIST = "fire_resist"         # 火抗
    ICE_RESIST = "ice_resist"           # 冰抗
    THUNDER_RESIST = "thunder_resist"   # 雷抗
    POISON_RESIST = "poison_resist"     # 毒抗
    
    # 特殊属性
    SPEED = "speed"                     # 移动速度
    RECOVERY = "recovery"               # 回复速度

    # 高级属性
    ELEMENT_PENETRATION = "element_penetration"  # 元素穿透
    ELEMENT_DAMAGE = "element_damage"            # 元素伤害
    IGNORE_DEFENSE = "ignore_defense"            # 无视防御
    DAMAGE_REDUCTION = "damage_reduction"        # 伤害减免
    LIFE_STEAL = "life_steal"                    # 生命偷取
    CONTROL_POWER = "control_power"              # 控制强度
    CONTROL_RESIST = "control_resist"            # 控制抗性
    COOLDOWN_REDUCTION = "cooldown_reduction"    # 冷却缩减
    HEAL_BONUS = "heal_bonus"                    # 治疗加成
    MAGIC_SHIELD = "magic_shield"                # 法术护盾
    KNOCKBACK_RESIST = "knockback_resist"        # 击退抗性
    SLOW_RESIST = "slow_resist"                  # 减速抗性
    FREEZE_CHANCE = "freeze_chance"              # 冰冻概率
    ELEMENT_RESIST = "element_resist"            # 元素抗性
    LUCK = "luck"                                # 幸运值
    TREASURE_FIND = "treasure_find"              # 寻宝
    BATTLE_SPIRIT = "battle_spirit"              # 战意
    LEADERSHIP = "leadership"                    # 领导力
    TEAM_BUFF = "team_buff"                      # 团队增益


class ScenarioType(str, Enum):
    """场景类型枚举"""
    PVE = "pve"  # 玩家对环境
    PVP = "pvp"  # 玩家对玩家


class ProfessionType(str, Enum):
    """职业类型枚举"""
    SWORDSMAN = "swordsman"     # 剑客
    ARCHER = "archer"           # 弓手
    MAGE = "mage"              # 法师
    ASSASSIN = "assassin"       # 刺客
    MONK = "monk"              # 武僧
    DOCTOR = "doctor"           # 医师


class EquipmentAttribute(BaseModel):
    """装备属性"""
    type: AttributeType
    value: Union[int, float]
    is_percentage: bool = False
    
    class Config:
        use_enum_values = True


class Equipment(BaseModel):
    """装备基础模型"""
    id: str = Field(..., description="装备唯一ID")
    name: str = Field(..., description="装备名称")
    type: EquipmentType = Field(..., description="装备类型")
    level: int = Field(..., ge=1, le=100, description="装备等级")
    quality: str = Field(..., description="装备品质")
    
    # 属性列表
    attributes: List[EquipmentAttribute] = Field(default_factory=list, description="装备属性")
    
    # 套装信息
    set_name: Optional[str] = Field(None, description="套装名称")
    set_piece_count: Optional[int] = Field(None, description="套装件数")
    
    # 获取途径
    source: Optional[str] = Field(None, description="获取途径")

    # 扩展属性
    profession_suitable: Optional[List[str]] = Field(None, description="适合职业")
    scenario_rating: Optional[Dict[str, int]] = Field(None, description="场景评分")
    special_effects: Optional[List[str]] = Field(None, description="特殊效果")

    class Config:
        use_enum_values = True


class EquipmentScore(BaseModel):
    """装备评分模型"""
    equipment_id: str
    scenario: ScenarioType
    profession: ProfessionType
    
    # 评分详情
    total_score: float = Field(..., ge=0, le=100, description="总评分")
    attribute_scores: Dict[str, float] = Field(default_factory=dict, description="各属性评分")
    
    # 评分说明
    strengths: List[str] = Field(default_factory=list, description="优势")
    weaknesses: List[str] = Field(default_factory=list, description="劣势")
    recommendations: List[str] = Field(default_factory=list, description="建议")
    
    class Config:
        use_enum_values = True


class EquipmentComparison(BaseModel):
    """装备对比模型"""
    equipment_a: Equipment
    equipment_b: Equipment
    scenario: ScenarioType
    profession: ProfessionType
    
    # 对比结果
    winner: str = Field(..., description="更优装备ID")
    score_difference: float = Field(..., description="评分差异")
    comparison_details: Dict[str, str] = Field(default_factory=dict, description="详细对比")
    
    class Config:
        use_enum_values = True


class EquipmentRecommendation(BaseModel):
    """装备推荐模型"""
    profession: ProfessionType
    scenario: ScenarioType
    level_range: tuple[int, int] = Field(..., description="等级范围")
    
    # 推荐结果
    recommended_equipment: List[Equipment] = Field(default_factory=list, description="推荐装备")
    build_suggestions: List[str] = Field(default_factory=list, description="搭配建议")
    priority_attributes: List[AttributeType] = Field(default_factory=list, description="优先属性")
    
    class Config:
        use_enum_values = True
