<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逆水寒 AI Agent - 真正的智能对话</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .agent-status {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            margin-top: 10px;
            border-radius: 10px;
            font-size: 12px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.agent {
            justify-content: flex-start;
        }

        .message.system {
            justify-content: center;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.agent .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
        }

        .message.system .message-content {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
            border-radius: 15px;
            font-size: 12px;
            max-width: 50%;
        }

        .message-meta {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .agent-thinking {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 10px;
            margin: 10px 0;
            font-size: 12px;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .input-group input:focus {
            border-color: #007bff;
        }

        .input-group button {
            padding: 15px 25px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .input-group button:hover {
            background: #0056b3;
        }

        .input-group button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .example-questions {
            margin-top: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .example-btn {
            padding: 8px 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .example-btn:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .agent-capabilities {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            padding: 10px;
            border-radius: 10px;
            margin: 10px 0;
            font-size: 12px;
        }

        .agent-capabilities h4 {
            color: #155724;
            margin-bottom: 5px;
        }

        .agent-capabilities ul {
            margin-left: 15px;
        }

        .agent-capabilities li {
            color: #155724;
            margin-bottom: 2px;
        }

        .chat-mode-selector {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            justify-content: center;
        }

        .mode-btn {
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .mode-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .mode-btn.active {
            background: rgba(255,255,255,0.4);
            border-color: rgba(255,255,255,0.6);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 逆水寒 AI Agent</h1>
            <p>真正的智能对话助手 - 诚实、学习、进化</p>

            <!-- 聊天模式选择 -->
            <div class="chat-mode-selector">
                <button class="mode-btn active" id="gameMode" onclick="switchMode('game')">
                    🎮 游戏专家模式
                </button>
                <button class="mode-btn" id="generalMode" onclick="switchMode('general')">
                    💬 普通聊天模式
                </button>
            </div>

            <div class="agent-status" id="agentStatus">
                正在初始化Agent...
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message agent">
                <div class="message-content">
                    <strong>🤖 AI Agent 欢迎您！</strong><br><br>
                    
                    我是一个<strong>真正的AI Agent</strong>，具备以下能力：
                    
                    <div class="agent-capabilities">
                        <h4>🧠 我的核心能力：</h4>
                        <ul>
                            <li>✅ <strong>诚实回答</strong> - 我会承认不知道的事情</li>
                            <li>✅ <strong>意图理解</strong> - 我能理解您的真实需求</li>
                            <li>✅ <strong>持续学习</strong> - 我会从每次对话中学习</li>
                            <li>✅ <strong>智能决策</strong> - 我会选择最合适的回应方式</li>
                            <li>❌ <strong>不编造信息</strong> - 我不会虚构装备数据</li>
                        </ul>
                    </div>
                    
                    <strong>关于逆水寒手游：</strong><br>
                    我正在学习真实的游戏信息，目前知识有限。我会诚实告知我的能力边界，并提供可靠的信息来源。
                    
                    <div class="message-meta">
                        Agent初始化完成 | 准备对话
                    </div>
                </div>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Agent正在思考...</p>
        </div>

        <div class="chat-input">
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="问我关于逆水寒的任何问题..." maxlength="500">
                <button id="sendButton" onclick="sendMessage()">发送</button>
            </div>
            
            <div class="example-questions">
                <div class="example-btn" onclick="askExample('百炼武器有哪些？')">百炼武器有哪些？</div>
                <div class="example-btn" onclick="askExample('推荐一个PVP装备搭配')">推荐PVP装备搭配</div>
                <div class="example-btn" onclick="askExample('当前版本什么职业强？')">当前版本什么职业强？</div>
                <div class="example-btn" onclick="askExample('你能做什么？')">你能做什么？</div>
            </div>
        </div>
    </div>

    <script>
        let isLoading = false;
        let currentMode = 'game'; // 'game' 或 'general'

        // 页面加载时获取Agent状态
        window.onload = function() {
            updateAgentStatus();
            
            // 回车发送消息
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !isLoading) {
                    sendMessage();
                }
            });
        };

        async function updateAgentStatus() {
            try {
                const response = await fetch('/api/v1/learning/agent/status');
                const data = await response.json();
                
                const statusElement = document.getElementById('agentStatus');
                if (data.agent_initialized) {
                    const status = data.status;
                    statusElement.innerHTML = `
                        🟢 Agent已激活 | 
                        对话次数: ${status.conversation_count} | 
                        知识条目: ${status.knowledge_items} | 
                        学习需求: ${status.learning_needs}
                    `;
                } else {
                    statusElement.innerHTML = '🟡 Agent待激活 - 发送消息开始对话';
                }
            } catch (error) {
                document.getElementById('agentStatus').innerHTML = '🔴 Agent状态未知';
            }
        }

        function switchMode(mode) {
            currentMode = mode;

            // 更新按钮状态
            document.getElementById('gameMode').classList.toggle('active', mode === 'game');
            document.getElementById('generalMode').classList.toggle('active', mode === 'general');

            // 更新输入框提示
            const input = document.getElementById('messageInput');
            if (mode === 'game') {
                input.placeholder = '问我关于逆水寒的任何问题...';
                updateExampleQuestions('game');
            } else {
                input.placeholder = '随便聊聊，我会结合逆水寒知识回答...';
                updateExampleQuestions('general');
            }

            // 添加模式切换消息
            const modeText = mode === 'game' ? '🎮 游戏专家模式' : '💬 普通聊天模式';
            addMessage('system', `已切换到 ${modeText}`);
        }

        function updateExampleQuestions(mode) {
            const container = document.querySelector('.example-questions');

            if (mode === 'game') {
                container.innerHTML = `
                    <div class="example-btn" onclick="askExample('百炼武器有哪些？')">百炼武器有哪些？</div>
                    <div class="example-btn" onclick="askExample('推荐一个PVP装备搭配')">推荐PVP装备搭配</div>
                    <div class="example-btn" onclick="askExample('当前版本什么职业强？')">当前版本什么职业强？</div>
                    <div class="example-btn" onclick="askExample('你能做什么？')">你能做什么？</div>
                `;
            } else {
                container.innerHTML = `
                    <div class="example-btn" onclick="askExample('今天天气怎么样？')">今天天气怎么样？</div>
                    <div class="example-btn" onclick="askExample('推荐一部电影')">推荐一部电影</div>
                    <div class="example-btn" onclick="askExample('聊聊最近的新闻')">聊聊最近的新闻</div>
                    <div class="example-btn" onclick="askExample('你觉得游戏和生活的关系？')">游戏与生活</div>
                `;
            }
        }

        function askExample(question) {
            document.getElementById('messageInput').value = question;
            sendMessage();
        }

        async function sendMessage() {
            if (isLoading) return;
            
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            
            // 显示加载状态
            setLoading(true);
            
            try {
                // 根据模式选择不同的API端点
                const endpoint = currentMode === 'game'
                    ? '/api/v1/learning/chat'  // 游戏专家模式
                    : '/api/v1/learning/general-chat';  // 普通聊天模式

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        context: null,
                        mode: currentMode
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 显示Agent回复
                    addMessage('agent', data.agent_response, {
                        intent: data.intent_analysis.intent.primary_intent,
                        confidence: data.intent_analysis.confidence,
                        action: data.intent_analysis.action_taken,
                        learned: data.learned_something
                    });
                } else {
                    // 显示错误回复
                    addMessage('agent', data.fallback_response, {
                        error: true,
                        errorMsg: data.error
                    });
                }
                
                // 更新Agent状态
                updateAgentStatus();
                
            } catch (error) {
                addMessage('agent', `🚨 连接错误：${error.message}\n\n请检查服务器是否正常运行。`, {
                    error: true
                });
            }
            
            setLoading(false);
        }

        function addMessage(type, content, meta = {}) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            let metaInfo = '';
            if (type === 'agent' && meta.intent) {
                metaInfo = `意图: ${meta.intent} | 置信度: ${(meta.confidence * 100).toFixed(0)}% | 行动: ${meta.action}`;
                if (meta.learned) {
                    metaInfo += ' | 🧠 已学习';
                }
            } else if (meta.error) {
                metaInfo = '系统错误回复';
            } else {
                metaInfo = new Date().toLocaleTimeString();
            }
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${content.replace(/\n/g, '<br>')}
                    <div class="message-meta">${metaInfo}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function setLoading(loading) {
            isLoading = loading;
            const loadingElement = document.getElementById('loading');
            const sendButton = document.getElementById('sendButton');
            
            if (loading) {
                loadingElement.classList.add('show');
                sendButton.disabled = true;
                sendButton.textContent = '思考中...';
            } else {
                loadingElement.classList.remove('show');
                sendButton.disabled = false;
                sendButton.textContent = '发送';
            }
        }
    </script>
</body>
</html>
