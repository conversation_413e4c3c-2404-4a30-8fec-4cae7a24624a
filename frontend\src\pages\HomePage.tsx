import React from 'react'
import { Card, Row, Col, Typo<PERSON>, Button, Space, Statistic } from 'antd'
import { 
  BarChartOutlined, 
  SwapOutlined, 
  StarOutlined, 
  RocketOutlined,
  ThunderboltOutlined,
  ShieldOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title, Paragraph } = Typography

const HomePage: React.FC = () => {
  const navigate = useNavigate()

  const features = [
    {
      icon: <BarChartOutlined style={{ fontSize: 24, color: '#1890ff' }} />,
      title: '智能装备分析',
      description: '基于DeepSeek AI的装备属性分析和评分系统',
      action: () => navigate('/analysis')
    },
    {
      icon: <SwapOutlined style={{ fontSize: 24, color: '#52c41a' }} />,
      title: '装备对比',
      description: '多维度对比装备性能，找出最优选择',
      action: () => navigate('/comparison')
    },
    {
      icon: <StarOutlined style={{ fontSize: 24, color: '#faad14' }} />,
      title: '个性化推荐',
      description: '根据职业和场景推荐最适合的装备搭配',
      action: () => navigate('/recommendation')
    }
  ]

  const stats = [
    { title: '装备数据', value: 17, suffix: '件' },
    { title: '支持职业', value: 6, suffix: '种' },
    { title: '分析场景', value: 2, suffix: '类' },
    { title: 'AI模型', value: 'DeepSeek R1/V3', suffix: '' }
  ]

  return (
    <div className="fade-in">
      {/* 欢迎区域 */}
      <Card style={{ marginBottom: 24, textAlign: 'center' }}>
        <Space direction="vertical" size="large">
          <div>
            <Title level={1}>
              ⚔️ 逆水寒智能装备助手
            </Title>
            <Paragraph style={{ fontSize: 16, color: '#666' }}>
              基于 DeepSeek R1/V3 模型的游戏装备分析与策略推荐系统
            </Paragraph>
          </div>
          
          <Space size="large">
            <Button 
              type="primary" 
              size="large" 
              icon={<RocketOutlined />}
              onClick={() => navigate('/analysis')}
            >
              开始分析
            </Button>
            <Button 
              size="large" 
              onClick={() => navigate('/equipment')}
            >
              浏览装备库
            </Button>
          </Space>
        </Space>
      </Card>

      {/* 统计数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {stats.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                suffix={stat.suffix}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 功能特性 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {features.map((feature, index) => (
          <Col span={8} key={index}>
            <Card
              hoverable
              style={{ height: '100%' }}
              onClick={feature.action}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div style={{ textAlign: 'center' }}>
                  {feature.icon}
                </div>
                <Title level={4} style={{ textAlign: 'center', margin: 0 }}>
                  {feature.title}
                </Title>
                <Paragraph style={{ textAlign: 'center', margin: 0 }}>
                  {feature.description}
                </Paragraph>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 技术优势 */}
      <Card title="🚀 技术优势" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Space>
              <ThunderboltOutlined style={{ color: '#faad14', fontSize: 20 }} />
              <div>
                <Title level={5} style={{ margin: 0 }}>高性能AI分析</Title>
                <Paragraph style={{ margin: 0, color: '#666' }}>
                  DeepSeek R1推理模型，专业游戏策略分析
                </Paragraph>
              </div>
            </Space>
          </Col>
          <Col span={8}>
            <Space>
              <ShieldOutlined style={{ color: '#52c41a', fontSize: 20 }} />
              <div>
                <Title level={5} style={{ margin: 0 }}>完全合规</Title>
                <Paragraph style={{ margin: 0, color: '#666' }}>
                  仅提供数据分析，不涉及游戏修改
                </Paragraph>
              </div>
            </Space>
          </Col>
          <Col span={8}>
            <Space>
              <BarChartOutlined style={{ color: '#1890ff', fontSize: 20 }} />
              <div>
                <Title level={5} style={{ margin: 0 }}>数据驱动</Title>
                <Paragraph style={{ margin: 0, color: '#666' }}>
                  基于真实游戏数据的科学分析
                </Paragraph>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 使用说明 */}
      <Card title="📋 使用说明">
        <Row gutter={16}>
          <Col span={12}>
            <Title level={5}>🎯 PVP场景</Title>
            <Paragraph>
              • 注重爆发伤害和生存能力<br/>
              • 优先暴击率、暴击伤害、闪避率<br/>
              • 适合竞技场、帮战等玩法
            </Paragraph>
          </Col>
          <Col span={12}>
            <Title level={5}>🏰 PVE场景</Title>
            <Paragraph>
              • 注重持续输出和团队配合<br/>
              • 优先攻击力、抗性、回复速度<br/>
              • 适合副本、日常任务等玩法
            </Paragraph>
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default HomePage
