import React from 'react'
import { Card, Typography, Alert } from 'antd'
import { SwapOutlined } from '@ant-design/icons'

const { Title } = Typography

const EquipmentComparison: React.FC = () => {
  return (
    <div className="fade-in">
      <Card title={
        <div>
          <SwapOutlined /> 装备对比
        </div>
      }>
        <Alert
          message="功能开发中"
          description="装备对比功能正在开发中，敬请期待！"
          type="info"
          showIcon
        />
        
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <Title level={3} style={{ color: '#999' }}>
            🚧 功能开发中...
          </Title>
          <p style={{ color: '#666' }}>
            即将支持多件装备的详细对比分析
          </p>
        </div>
      </Card>
    </div>
  )
}

export default EquipmentComparison
