"""
装备业务逻辑服务
"""
import json
import os
from typing import List, Optional, Dict, Any
from loguru import logger

from ..models.equipment import (
    Equipment, EquipmentAttribute, EquipmentType, AttributeType,
    ScenarioType, ProfessionType
)
from ..core.config import get_settings

settings = get_settings()


class EquipmentService:
    """装备服务类"""

    def __init__(self):
        self.equipment_cache: Dict[str, Equipment] = {}
        self.pve_data: Dict[str, Any] = {}
        self.pvp_data: Dict[str, Any] = {}
        self.load_equipment_data()
    
    def load_equipment_data(self):
        """加载装备数据"""
        try:
            equipment_dir = settings.EQUIPMENT_DATA_PATH
            if not os.path.exists(equipment_dir):
                logger.warning(f"装备数据目录不存在: {equipment_dir}")
                self._create_sample_data()
                return

            # 加载所有JSON文件，包括扩展数据
            for filename in os.listdir(equipment_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(equipment_dir, filename)
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    if isinstance(data, list):
                        for item in data:
                            equipment = self._parse_equipment_data(item)
                            if equipment:
                                self.equipment_cache[equipment.id] = equipment
                    else:
                        equipment = self._parse_equipment_data(data)
                        if equipment:
                            self.equipment_cache[equipment.id] = equipment

            logger.info(f"已加载 {len(self.equipment_cache)} 件装备数据")

            # 加载场景数据
            self._load_scenario_data()

        except Exception as e:
            logger.error(f"加载装备数据失败: {e}")
            self._create_sample_data()
    
    def _parse_equipment_data(self, data: Dict[str, Any]) -> Optional[Equipment]:
        """解析装备数据"""
        try:
            attributes = []
            for attr_data in data.get('attributes', []):
                attributes.append(EquipmentAttribute(
                    type=attr_data['type'],
                    value=attr_data['value'],
                    is_percentage=attr_data.get('is_percentage', False)
                ))
            
            return Equipment(
                id=data['id'],
                name=data['name'],
                type=data['type'],
                level=data['level'],
                quality=data['quality'],
                attributes=attributes,
                set_name=data.get('set_name'),
                set_piece_count=data.get('set_piece_count'),
                source=data.get('source')
            )
            
        except Exception as e:
            logger.error(f"解析装备数据失败: {e}, data: {data}")
            return None
    
    def _create_sample_data(self):
        """创建示例数据"""
        sample_equipment = [
            Equipment(
                id="sword_001",
                name="烈焰长剑",
                type=EquipmentType.WEAPON,
                level=50,
                quality="紫色",
                attributes=[
                    EquipmentAttribute(type=AttributeType.ATTACK, value=1200),
                    EquipmentAttribute(type=AttributeType.CRIT_RATE, value=15, is_percentage=True),
                    EquipmentAttribute(type=AttributeType.CRIT_DAMAGE, value=25, is_percentage=True)
                ],
                source="副本掉落"
            ),
            Equipment(
                id="armor_001",
                name="玄铁护甲",
                type=EquipmentType.ARMOR,
                level=50,
                quality="紫色",
                attributes=[
                    EquipmentAttribute(type=AttributeType.DEFENSE, value=800),
                    EquipmentAttribute(type=AttributeType.HP, value=5000),
                    EquipmentAttribute(type=AttributeType.FIRE_RESIST, value=20, is_percentage=True)
                ],
                source="商店购买"
            ),
            Equipment(
                id="ring_001",
                name="疾风戒指",
                type=EquipmentType.RING,
                level=45,
                quality="蓝色",
                attributes=[
                    EquipmentAttribute(type=AttributeType.ATTACK, value=300),
                    EquipmentAttribute(type=AttributeType.SPEED, value=10, is_percentage=True),
                    EquipmentAttribute(type=AttributeType.HIT_RATE, value=8, is_percentage=True)
                ],
                source="任务奖励"
            )
        ]
        
        for equipment in sample_equipment:
            self.equipment_cache[equipment.id] = equipment
        
        logger.info("已创建示例装备数据")
    
    async def get_equipment_by_id(self, equipment_id: str) -> Optional[Equipment]:
        """根据ID获取装备"""
        return self.equipment_cache.get(equipment_id)
    
    async def get_equipment_by_criteria(
        self,
        level_range: tuple[int, int] = (1, 100),
        equipment_type: Optional[EquipmentType] = None,
        quality: Optional[str] = None
    ) -> List[Equipment]:
        """根据条件筛选装备"""
        result = []
        
        for equipment in self.equipment_cache.values():
            # 等级筛选
            if not (level_range[0] <= equipment.level <= level_range[1]):
                continue
            
            # 类型筛选
            if equipment_type and equipment.type != equipment_type:
                continue
            
            # 品质筛选
            if quality and equipment.quality != quality:
                continue
            
            result.append(equipment)
        
        return result
    
    async def get_equipment_list(
        self,
        equipment_type: Optional[EquipmentType] = None,
        level_range: tuple[int, int] = (1, 100),
        quality: Optional[str] = None,
        limit: int = 50
    ) -> List[Equipment]:
        """获取装备列表"""
        equipment_list = await self.get_equipment_by_criteria(
            level_range=level_range,
            equipment_type=equipment_type,
            quality=quality
        )
        
        # 按等级和品质排序
        quality_order = {"橙色": 4, "紫色": 3, "蓝色": 2, "绿色": 1, "白色": 0}
        equipment_list.sort(
            key=lambda x: (x.level, quality_order.get(x.quality, 0)),
            reverse=True
        )
        
        return equipment_list[:limit]
    
    def get_priority_attributes(
        self, 
        profession: ProfessionType, 
        scenario: ScenarioType
    ) -> List[AttributeType]:
        """获取职业在特定场景下的优先属性"""
        
        # 职业基础属性偏好
        profession_attributes = {
            ProfessionType.SWORDSMAN: [
                AttributeType.ATTACK, AttributeType.CRIT_RATE, 
                AttributeType.DEFENSE, AttributeType.HP
            ],
            ProfessionType.ARCHER: [
                AttributeType.ATTACK, AttributeType.CRIT_DAMAGE,
                AttributeType.HIT_RATE, AttributeType.SPEED
            ],
            ProfessionType.MAGE: [
                AttributeType.ATTACK, AttributeType.MP,
                AttributeType.CRIT_RATE, AttributeType.RECOVERY
            ],
            ProfessionType.ASSASSIN: [
                AttributeType.ATTACK, AttributeType.CRIT_RATE,
                AttributeType.CRIT_DAMAGE, AttributeType.SPEED
            ],
            ProfessionType.MONK: [
                AttributeType.HP, AttributeType.DEFENSE,
                AttributeType.RECOVERY, AttributeType.ATTACK
            ],
            ProfessionType.DOCTOR: [
                AttributeType.MP, AttributeType.RECOVERY,
                AttributeType.HP, AttributeType.DEFENSE
            ]
        }
        
        base_attributes = profession_attributes.get(profession, [])
        
        # 根据场景调整优先级
        if scenario == ScenarioType.PVP:
            # PVP更注重爆发和生存
            pvp_bonus = [AttributeType.CRIT_DAMAGE, AttributeType.DODGE_RATE]
            return pvp_bonus + base_attributes
        else:
            # PVE更注重持续输出和抗性
            pve_bonus = [AttributeType.FIRE_RESIST, AttributeType.ICE_RESIST]
            return base_attributes + pve_bonus
    
    def calculate_attribute_weight(
        self,
        attribute_type: AttributeType,
        profession: ProfessionType,
        scenario: ScenarioType
    ) -> float:
        """计算属性权重"""
        priority_attrs = self.get_priority_attributes(profession, scenario)
        
        if attribute_type in priority_attrs:
            # 根据优先级位置计算权重
            index = priority_attrs.index(attribute_type)
            return max(1.0 - index * 0.1, 0.3)  # 权重范围 0.3-1.0
        
        return 0.2  # 非优先属性的基础权重

    def _load_scenario_data(self):
        """加载场景数据"""
        try:
            # 加载PVE数据
            pve_path = "data/scenarios/pve_dungeons.json"
            if os.path.exists(pve_path):
                with open(pve_path, 'r', encoding='utf-8') as f:
                    self.pve_data = json.load(f)
                logger.info("已加载PVE场景数据")

            # 加载PVP数据
            pvp_path = "data/scenarios/pvp_meta.json"
            if os.path.exists(pvp_path):
                with open(pvp_path, 'r', encoding='utf-8') as f:
                    self.pvp_data = json.load(f)
                logger.info("已加载PVP场景数据")

        except Exception as e:
            logger.error(f"加载场景数据失败: {e}")

    def get_scenario_specific_weights(
        self,
        profession: ProfessionType,
        scenario: ScenarioType,
        context: Optional[str] = None
    ) -> Dict[str, float]:
        """获取场景特定的属性权重"""

        if scenario == ScenarioType.PVP:
            # 从PVP数据获取权重
            pvp_weights = self.pvp_data.get("general_pvp_strategies", {}).get("attribute_weights", {})
            profession_weights = pvp_weights.get("profession_specific", {}).get(profession.value, {})
            general_weights = pvp_weights.get("pvp_general", {})

            # 合并权重，职业特定权重优先
            weights = {**general_weights, **profession_weights}

        else:  # PVE
            # 从PVE数据获取权重
            pve_weights = self.pve_data.get("general_pve_strategies", {}).get("attribute_weights", {})
            weights = pve_weights.get("pve_general", {})

            # 如果指定了具体副本，使用副本特定权重
            if context:
                for dungeon in self.pve_data.get("dungeons", []):
                    if dungeon["id"] == context:
                        dungeon_weights = dungeon.get("recommended_equipment", {}).get("priority_attributes", [])
                        for attr_info in dungeon_weights:
                            weights[attr_info["type"]] = attr_info["weight"]
                        break

        return weights

    def get_equipment_rating(
        self,
        equipment: Equipment,
        profession: ProfessionType,
        scenario: ScenarioType,
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取装备在特定场景下的评级"""

        # 获取场景特定权重
        weights = self.get_scenario_specific_weights(profession, scenario, context)

        # 计算属性评分
        attribute_scores = {}
        total_score = 0
        max_possible_score = 0

        for attr in equipment.attributes:
            attr_type = attr.type.value if hasattr(attr.type, 'value') else str(attr.type)
            weight = weights.get(attr_type, 0.1)

            # 根据属性类型和数值计算评分
            if attr.is_percentage:
                # 百分比属性，通常0-100%
                score = min(attr.value * 2, 100)  # 50%属性得100分
            else:
                # 绝对数值属性，需要根据类型标准化
                if attr_type in ["attack", "defense", "hp", "mp"]:
                    # 主要属性，基于等级标准化
                    expected_value = equipment.level * 30  # 简化计算
                    score = min((attr.value / expected_value) * 100, 100)
                else:
                    # 其他属性
                    score = min(attr.value / 50 * 100, 100)

            weighted_score = score * weight
            attribute_scores[attr_type] = score
            total_score += weighted_score
            max_possible_score += 100 * weight

        # 标准化总分到0-100
        if max_possible_score > 0:
            normalized_score = (total_score / max_possible_score) * 100
        else:
            normalized_score = 50  # 默认分数

        # 检查装备是否适合该职业
        equipment_data = equipment.model_dump() if hasattr(equipment, 'model_dump') else equipment.__dict__
        suitable_professions = equipment_data.get('profession_suitable', [])
        if suitable_professions and profession.value not in suitable_professions:
            normalized_score *= 0.8  # 不适合的职业减分

        # 检查场景评级
        scenario_rating = equipment_data.get('scenario_rating', {})
        if scenario_rating:
            scenario_score = scenario_rating.get(scenario.value, normalized_score)
            normalized_score = (normalized_score + scenario_score) / 2

        return {
            "total_score": round(normalized_score, 1),
            "attribute_scores": attribute_scores,
            "weights_used": weights,
            "profession_suitable": profession.value in suitable_professions if suitable_professions else True,
            "scenario_rating": scenario_rating.get(scenario.value) if scenario_rating else None
        }

    def get_meta_recommendations(
        self,
        profession: ProfessionType,
        scenario: ScenarioType,
        mode: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取当前meta的装备推荐"""

        recommendations = {
            "tier_s_equipment": [],
            "tier_a_equipment": [],
            "meta_builds": [],
            "counter_strategies": {}
        }

        if scenario == ScenarioType.PVP:
            # 从PVP meta数据获取推荐
            for pvp_mode in self.pvp_data.get("pvp_modes", []):
                if mode and pvp_mode["id"] != mode:
                    continue

                current_meta = pvp_mode.get("current_meta", {})

                # 获取S级构建
                for build in current_meta.get("tier_s_builds", []):
                    if build["profession"] == profession.value:
                        recommendations["meta_builds"].append({
                            "name": build["build_name"],
                            "equipment": build["core_equipment"],
                            "attributes": build["key_attributes"],
                            "strategy": build["strategy"],
                            "win_rate": build["win_rate"]
                        })

                # 获取反制策略
                counter_strategies = self.pvp_data.get("general_pvp_strategies", {}).get("counter_strategies", {})
                recommendations["counter_strategies"] = counter_strategies

        else:  # PVE
            # 从PVE数据获取推荐
            for dungeon in self.pve_data.get("dungeons", []):
                strategies = dungeon.get("recommended_equipment", {}).get("profession_strategies", {})
                if profession.value in strategies:
                    strategy = strategies[profession.value]
                    recommendations["meta_builds"].append({
                        "dungeon": dungeon["name"],
                        "role": strategy["role"],
                        "key_attributes": strategy["key_attributes"],
                        "equipment_focus": strategy["equipment_focus"]
                    })

        return recommendations


# 创建全局服务实例
equipment_service = EquipmentService()
