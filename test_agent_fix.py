#!/usr/bin/env python3
"""
测试Agent修复效果
"""
import asyncio
import httpx

async def test_agent_fix():
    """测试Agent修复"""
    print("测试Agent修复效果")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=90.0) as client:
        
        # 测试Agent对话
        print("1. 测试Agent对话")
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/learning/chat",
                json={"message": "你好，你是什么模型？"}
            )
            
            print(f"   状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   成功: {data.get('success', False)}")
                
                if data.get("success"):
                    reply = data["agent_response"]
                    print("   真正的AI回复:")
                    if len(reply) > 200:
                        print(f"   {reply[:200]}...")
                    else:
                        print(f"   {reply}")
                else:
                    print("   使用了降级回复:")
                    print(f"   错误: {data.get('error', '未知')}")
                    fallback = data.get("fallback_response", "")
                    if len(fallback) > 200:
                        print(f"   降级内容: {fallback[:200]}...")
            else:
                print(f"   HTTP错误: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except Exception as e:
            print(f"   异常: {e}")

async def main():
    await test_agent_fix()

if __name__ == "__main__":
    asyncio.run(main())
