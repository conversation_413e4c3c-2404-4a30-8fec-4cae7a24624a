"""
应用配置模块
"""
from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "逆水寒智能装备助手"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # API配置
    API_V1_STR: str = "/api/v1"
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # SiliconFlow API配置 (DeepSeek模型)
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_BASE_URL: str = "https://api.siliconflow.cn/v1"
    DEEPSEEK_MODEL_V3: str = "deepseek-ai/deepseek-v2.5"
    DEEPSEEK_MODEL_R1: str = "Pro/deepseek-ai/DeepSeek-R1"
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./nsh_agent.db"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS配置
    ALLOWED_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173"
    ]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # 游戏数据配置
    EQUIPMENT_DATA_PATH: str = "data/equipment"
    PROFESSION_DATA_PATH: str = "data/professions"
    ATTRIBUTE_DATA_PATH: str = "data/attributes"
    
    # 缓存配置
    CACHE_TTL: int = 3600  # 1小时
    
    # 限流配置
    RATE_LIMIT_PER_MINUTE: int = 60

    # 数据更新配置
    UPDATE_INTERVAL_HOURS: int = 2
    SOCIAL_MEDIA_FETCH_LIMIT: int = 50
    ENABLE_REAL_TIME_UPDATE: bool = True
    AUTO_UPDATE_ON_STARTUP: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
