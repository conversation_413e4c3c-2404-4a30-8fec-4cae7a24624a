# 逆水寒智能装备助手 (NSH Agent)

基于 DeepSeek R1/V3 模型的逆水寒游戏装备分析与策略推荐系统

## 🎯 功能特性

### 核心功能
- **装备评分系统**: 基于属性权重的智能评分
- **PVP/PVE推荐**: 针对不同场景的装备搭配建议
- **职业优化**: 各职业流派的装备配置方案
- **数据分析**: 装备属性趋势与市场分析

### 技术特点
- 使用 DeepSeek API 进行智能分析
- 现代化 Web 界面
- 实时数据处理
- 可扩展的插件架构

## 🛠️ 技术栈

- **后端**: Python + FastAPI
- **前端**: React + TypeScript + Vite
- **AI模型**: DeepSeek R1/V3 API
- **数据库**: SQLite/PostgreSQL
- **部署**: Docker + Nginx

## 📁 项目结构

```
nsh_agent/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── requirements.txt
│   └── main.py
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   └── utils/          # 工具函数
│   ├── package.json
│   └── vite.config.ts
├── data/                   # 游戏数据
│   ├── equipment/          # 装备数据
│   ├── professions/        # 职业数据
│   └── attributes/         # 属性配置
├── docs/                   # 文档
└── docker-compose.yml      # 容器编排
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Node.js 18+
- Docker (可选)

### 安装步骤
1. 克隆项目
2. 配置 DeepSeek API Key
3. 启动后端服务
4. 启动前端应用

## 📊 商业模式

- **免费版**: 基础装备评分
- **高级版**: 完整分析报告 (29元/月)
- **专业版**: 实时数据 + 定制建议 (99元/月)

## 🔒 合规说明

本工具仅提供数据分析和策略建议，不涉及任何游戏修改或自动化操作，完全符合游戏服务条款。

## 💰 商业价值分析

### 市场潜力
- **目标用户**: 逆水寒1亿用户的1-5% (100万-500万潜在用户)
- **付费转化率**: 预估2-10%
- **年收入潜力**: 720万-2.5亿元

### 成本优势
- **DeepSeek API**: 每百万tokens仅0.1-1元，比GPT-4便宜20倍
- **单次分析成本**: 约0.0005-0.001元
- **月运营成本**: 预估10-100元 (中等使用量)

### 商业模式
- **免费版**: 基础装备评分，每日3次分析
- **高级版**: 完整分析报告 + 装备对比 (29元/月)
- **专业版**: 实时推荐 + 定制建议 + API访问 (99元/月)

## 🚀 快速开始

### 一键启动 (推荐)
```bash
# 1. 获取 DeepSeek API Key
# 访问: https://platform.deepseek.com/

# 2. 配置环境
cp backend/.env.example backend/.env
# 编辑 backend/.env，填入您的 DEEPSEEK_API_KEY

# 3. 启动项目
./start.sh

# 4. 访问应用
# 前端: http://localhost:3000
# API文档: http://localhost:8000/docs
```

### 本地开发
```bash
# 后端
cd backend
pip install -r requirements.txt
python main.py

# 前端 (新终端)
cd frontend
npm install
npm run dev
```

## 📊 项目状态

✅ **已完成**
- 项目架构设计
- 后端API框架 (FastAPI)
- DeepSeek AI集成
- 装备数据模型
- 前端界面框架 (React + Ant Design)
- Docker部署配置
- 示例数据 (17件装备)

🚧 **开发中**
- 装备对比功能
- 推荐算法优化
- 用户认证系统
- 数据库集成

📋 **计划中**
- 移动端适配
- 更多游戏数据
- 社区功能
- 付费系统集成
