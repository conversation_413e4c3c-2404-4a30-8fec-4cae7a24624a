#!/usr/bin/env python3
"""
测试Agent是否在现编内容
"""
import asyncio
import httpx

async def test_agent_accuracy():
    """测试Agent准确性"""
    print("测试Agent是否在现编内容")
    print("=" * 60)
    
    # 测试一些具体的游戏问题，看Agent是否会编造
    test_questions = [
        {
            "question": "逆水寒手游当前是什么版本？",
            "check_for": ["版本号", "具体数字", "时间"]
        },
        {
            "question": "逆水寒手游有多少个职业？",
            "check_for": ["具体数字", "职业名称"]
        },
        {
            "question": "逆水寒手游最新的副本叫什么名字？",
            "check_for": ["副本名称", "具体地名"]
        },
        {
            "question": "逆水寒手游的满级是多少级？",
            "check_for": ["等级数字"]
        },
        {
            "question": "逆水寒手游今天有什么活动？",
            "check_for": ["具体活动", "时间"]
        }
    ]
    
    async with httpx.AsyncClient(timeout=90.0) as client:
        
        for i, test_case in enumerate(test_questions, 1):
            print(f"\n测试 {i}: {test_case['question']}")
            print("-" * 50)
            
            try:
                response = await client.post(
                    "http://localhost:8000/api/v1/learning/chat",
                    json={"message": test_case["question"]}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        reply = data["agent_response"]
                        print("Agent回复:")
                        print(reply)
                        
                        # 分析回复是否可能是现编的
                        print("\n分析:")
                        suspicious_phrases = [
                            "1.2版本", "1.3版本", "2023年", "2024年",
                            "洞庭问剑", "白石神殿", "镜天阁",
                            "截至", "当前赛季", "最新调整"
                        ]
                        
                        found_suspicious = []
                        for phrase in suspicious_phrases:
                            if phrase in reply:
                                found_suspicious.append(phrase)
                        
                        if found_suspicious:
                            print(f"⚠️ 可能现编的内容: {', '.join(found_suspicious)}")
                        
                        # 检查是否承认不知道
                        honest_phrases = [
                            "不确定", "不知道", "无法确认", "需要查证",
                            "建议查看官方", "我的信息可能不准确"
                        ]
                        
                        found_honest = []
                        for phrase in honest_phrases:
                            if phrase in reply:
                                found_honest.append(phrase)
                        
                        if found_honest:
                            print(f"✅ 诚实表达: {', '.join(found_honest)}")
                        else:
                            print("❌ 没有承认不确定性，可能在现编")
                            
                    else:
                        print(f"Agent失败: {data.get('error')}")
                else:
                    print(f"HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"异常: {e}")
            
            await asyncio.sleep(3)
    
    print("\n" + "=" * 60)
    print("准确性测试完成")
    
    print("\n建议:")
    print("1. 如果Agent给出具体数据但没有说明来源，可能在现编")
    print("2. 诚实的AI应该承认不确定性")
    print("3. 应该引导用户查看官方信息")

async def main():
    await test_agent_accuracy()

if __name__ == "__main__":
    asyncio.run(main())
