"""
SiliconFlow API 服务模块 (DeepSeek模型)
"""
import httpx
import json
from typing import Dict, List, Optional, Any
from loguru import logger

from ..core.config import get_settings
from ..models.equipment import Equipment, EquipmentScore, ScenarioType, ProfessionType

settings = get_settings()


class DeepSeekService:
    """SiliconFlow API 服务类 (DeepSeek模型)"""
    
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.base_url = settings.DEEPSEEK_BASE_URL
        self.model_v3 = settings.DEEPSEEK_MODEL_V3
        self.model_r1 = settings.DEEPSEEK_MODEL_R1
        
        if not self.api_key:
            logger.warning("SiliconFlow API Key 未配置")
    
    async def _make_request(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> Optional[str]:
        """发送请求到 SiliconFlow API"""
        if not self.api_key:
            logger.error("SiliconFlow API Key 未配置")
            return None
        
        model = model or self.model_v3
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": False
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except httpx.HTTPError as e:
            logger.error(f"SiliconFlow API 请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"SiliconFlow API 调用异常: {e}")
            return None
    
    async def analyze_equipment(
        self, 
        equipment: Equipment, 
        scenario: ScenarioType, 
        profession: ProfessionType
    ) -> Optional[EquipmentScore]:
        """分析装备并给出评分"""
        
        # 构建分析提示词
        prompt = self._build_equipment_analysis_prompt(equipment, scenario, profession)
        
        messages = [
            {
                "role": "system",
                "content": "你是一个逆水寒游戏的装备分析专家，擅长根据不同职业和场景评估装备价值。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        # 使用 R1 模型进行推理分析
        response = await self._make_request(messages, model=self.model_r1, temperature=0.3)
        
        if not response:
            return None
        
        try:
            # 解析 AI 响应并构建评分对象
            return self._parse_equipment_analysis(response, equipment.id, scenario, profession)
        except Exception as e:
            logger.error(f"解析装备分析结果失败: {e}")
            return None
    
    def _build_equipment_analysis_prompt(
        self,
        equipment: Equipment,
        scenario: ScenarioType,
        profession: ProfessionType
    ) -> str:
        """构建装备分析提示词"""

        attributes_text = "\n".join([
            f"- {attr.type}: {attr.value}{'%' if attr.is_percentage else ''}"
            for attr in equipment.attributes
        ])

        # 获取特殊效果
        special_effects = ""
        if equipment.special_effects:
            special_effects = "\n特殊效果：\n" + "\n".join([f"- {effect}" for effect in equipment.special_effects])

        # 获取适合职业信息
        suitable_info = ""
        if equipment.profession_suitable:
            suitable_info = f"\n推荐职业：{', '.join(equipment.profession_suitable)}"

        # 获取场景评分
        scenario_info = ""
        if equipment.scenario_rating:
            scenario_info = f"\n场景评分：PVP {equipment.scenario_rating.get('pvp', 'N/A')}分，PVE {equipment.scenario_rating.get('pve', 'N/A')}分"

        # 根据场景提供更具体的分析要求
        scenario_specific = ""
        if scenario == ScenarioType.PVP:
            scenario_specific = """
PVP场景分析要点：
- 重点关注爆发能力（暴击率、暴击伤害）
- 评估生存能力（闪避率、生命值、防御力）
- 考虑机动性（速度、控制抗性）
- 分析对战优势（穿透、无视防御等）
"""
        else:
            scenario_specific = """
PVE场景分析要点：
- 重点关注持续输出能力
- 评估团队配合价值
- 考虑副本特殊需求（抗性、特殊属性）
- 分析生存和续航能力
"""

        prompt = f"""
作为逆水寒游戏的专业装备分析师，请深入分析以下装备：

装备信息：
- 名称：{equipment.name}
- 类型：{equipment.type}
- 等级：{equipment.level}
- 品质：{equipment.quality}
- 套装：{equipment.set_name or '无'}
- 获取途径：{equipment.source or '未知'}
{suitable_info}
{scenario_info}

装备属性：
{attributes_text}
{special_effects}

分析条件：
- 职业：{profession.value}
- 场景：{scenario.value}

{scenario_specific}

请从以下维度进行专业分析：
1. 总体评分（0-100分，考虑职业适配度和场景需求）
2. 各属性的价值评估（每个属性0-100分）
3. 该装备的核心优势（3-5个要点）
4. 存在的劣势或不足（2-3个要点）
5. 针对该职业和场景的具体使用建议（3-4个建议）
6. 装备搭配建议（推荐配套装备类型）

请以JSON格式返回分析结果：
{{
    "total_score": 85.5,
    "attribute_scores": {{
        "attack": 90,
        "crit_rate": 85,
        "crit_damage": 88,
        "speed": 75
    }},
    "strengths": [
        "攻击力出色，适合输出型玩家",
        "暴击属性优秀，爆发能力强",
        "特殊效果实用，提升战斗效率"
    ],
    "weaknesses": [
        "防御力相对较低，生存能力有限",
        "缺乏抗性属性，面对特殊伤害时脆弱"
    ],
    "recommendations": [
        "建议搭配防御型装备提升生存能力",
        "优先强化暴击相关属性",
        "适合与治疗职业组队",
        "在{scenario.value}场景下发挥最佳效果"
    ],
    "equipment_synergy": [
        "推荐搭配高防御护甲",
        "建议选择生命值加成饰品",
        "可配合抗性类装备"
    ]
}}
"""
        return prompt
    
    def _parse_equipment_analysis(
        self, 
        response: str, 
        equipment_id: str, 
        scenario: ScenarioType, 
        profession: ProfessionType
    ) -> EquipmentScore:
        """解析装备分析结果"""
        
        try:
            # 尝试从响应中提取JSON
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("响应中未找到有效的JSON")
            
            json_str = response[start_idx:end_idx]
            data = json.loads(json_str)
            
            return EquipmentScore(
                equipment_id=equipment_id,
                scenario=scenario,
                profession=profession,
                total_score=data.get("total_score", 0),
                attribute_scores=data.get("attribute_scores", {}),
                strengths=data.get("strengths", []),
                weaknesses=data.get("weaknesses", []),
                recommendations=data.get("recommendations", []) + data.get("equipment_synergy", [])
            )
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"解析JSON失败: {e}")
            # 返回默认评分
            return EquipmentScore(
                equipment_id=equipment_id,
                scenario=scenario,
                profession=profession,
                total_score=50.0,
                strengths=["需要进一步分析"],
                weaknesses=["分析数据不完整"],
                recommendations=["建议重新评估"]
            )
    
    async def get_equipment_recommendations(
        self, 
        profession: ProfessionType, 
        scenario: ScenarioType,
        level_range: tuple[int, int],
        available_equipment: List[Equipment]
    ) -> List[str]:
        """获取装备推荐建议"""
        
        prompt = f"""
作为逆水寒装备专家，请为以下条件推荐最佳装备搭配：

条件：
- 职业：{profession}
- 场景：{scenario}
- 等级范围：{level_range[0]}-{level_range[1]}

可选装备数量：{len(available_equipment)}

请提供：
1. 推荐的装备搭配方案
2. 属性优先级建议
3. 搭配理由和策略说明

请以简洁明了的文字形式回答。
"""
        
        messages = [
            {
                "role": "system",
                "content": "你是逆水寒游戏的装备搭配专家，能够根据职业特点和场景需求提供最优的装备建议。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        response = await self._make_request(messages, model=self.model_v3)
        
        if response:
            # 将响应按行分割作为建议列表
            return [line.strip() for line in response.split('\n') if line.strip()]
        
        return ["暂无推荐建议"]


# 创建全局服务实例
deepseek_service = DeepSeekService()
