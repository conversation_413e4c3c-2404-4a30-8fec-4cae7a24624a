import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout } from 'antd'
import Header from './components/Header'
import Sidebar from './components/Sidebar'
import HomePage from './pages/HomePage'
import EquipmentAnalysis from './pages/EquipmentAnalysis'
import EquipmentComparison from './pages/EquipmentComparison'
import EquipmentRecommendation from './pages/EquipmentRecommendation'
import EquipmentList from './pages/EquipmentList'
import LearningAgent from './pages/LearningAgent'

const { Content } = Layout

function App() {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header />
      <Layout>
        <Sidebar />
        <Layout style={{ padding: '24px' }}>
          <Content
            style={{
              padding: 24,
              margin: 0,
              minHeight: 280,
              background: '#fff',
              borderRadius: 8,
            }}
          >
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/analysis" element={<EquipmentAnalysis />} />
              <Route path="/comparison" element={<EquipmentComparison />} />
              <Route path="/recommendation" element={<EquipmentRecommendation />} />
              <Route path="/equipment" element={<EquipmentList />} />
              <Route path="/learning" element={<LearningAgent />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Layout>
  )
}

export default App
