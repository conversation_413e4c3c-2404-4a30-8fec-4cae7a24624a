
# 🚀 逆水寒智能装备助手 - 快速启动指南

## 📋 项目概述
基于 DeepSeek R1/V3 模型的逆水寒游戏装备分析与策略推荐系统

## 🎯 核心功能
- ⚔️  装备智能评分 (基于AI分析)
- 🆚 装备对比分析
- 📊 PVP/PVE场景推荐
- 👥 职业专属配置建议

## 🛠️ 技术栈
- **后端**: Python + FastAPI + DeepSeek API
- **前端**: React + TypeScript + Ant Design
- **部署**: Docker + Nginx

## 🚀 快速启动

### 方法一: Docker 一键启动 (推荐)
```bash
# 1. 配置API Key
cp backend/.env.example backend/.env
# 编辑 backend/.env，填入您的 DeepSeek API Key

# 2. 一键启动
./start.sh

# 3. 访问应用
# 前端: http://localhost:3000
# 后端API: http://localhost:8000/docs
```

### 方法二: 本地开发启动
```bash
# 后端启动
cd backend
pip install -r requirements.txt
cp .env.example .env  # 配置API Key
python main.py

# 前端启动 (新终端)
cd frontend
npm install
npm run dev
```

## 🔑 API Key 配置
本项目已配置SiliconFlow API (DeepSeek模型):
- API Key: 已预配置
- 模型: Pro/deepseek-ai/DeepSeek-R1
- 文档: https://docs.siliconflow.cn/cn/api-reference/chat-completions/chat-completions

如需更换API Key:
1. 访问: https://cloud.siliconflow.cn/
2. 注册账号并获取API Key
3. 在 backend/.env 中修改 DEEPSEEK_API_KEY

## 💰 成本估算
- SiliconFlow API: 高性价比，支持DeepSeek模型
- 单次装备分析约消耗500-1000 tokens
- 预计每月API成本: 10-50元 (中等使用量)
- 已预配置API Key，可直接使用

## 📊 商业模式
- 免费版: 基础装备评分
- 高级版: 完整分析报告 (29元/月)
- 专业版: 实时数据 + 定制建议 (99元/月)

## 🔒 合规说明
本工具仅提供数据分析和策略建议，不涉及任何游戏修改或自动化操作。

## 📞 技术支持
如有问题，请查看 docs/DEVELOPMENT.md 或提交 Issue。
