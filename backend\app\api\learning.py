"""
学习Agent API路由
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel
from loguru import logger

from ..services.learning_agent import learning_agent
from ..services.web_search_service import web_search_service
from ..services.deepseek_service import deepseek_service
from ..services.real_agent_service import RealAgentService

# 创建真正的Agent实例
real_agent = None

class ChatMessage(BaseModel):
    message: str
    context: Optional[Dict] = None

router = APIRouter(prefix="/learning", tags=["学习Agent"])


@router.get("/status")
async def get_learning_status():
    """获取学习Agent状态"""
    try:
        status = {
            "agent_status": "active",
            "last_update": learning_agent.last_update.isoformat() if learning_agent.last_update else None,
            "knowledge_base_size": len(learning_agent.knowledge_base),
            "update_count": learning_agent.knowledge_base.get("update_count", 0),
            "next_update_in": str(learning_agent.update_interval),
            "should_update": await learning_agent.should_update()
        }
        return status
    except Exception as e:
        logger.error(f"获取学习状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/update")
async def trigger_update(background_tasks: BackgroundTasks):
    """手动触发知识库更新"""
    try:
        # 在后台执行更新任务
        background_tasks.add_task(learning_agent.update_knowledge_base)
        
        return {
            "message": "知识库更新已启动",
            "status": "updating",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"触发更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search")
async def search_game_info(
    query: str,
    max_results: int = 10
):
    """搜索游戏信息"""
    try:
        if not query:
            raise HTTPException(status_code=400, detail="查询参数不能为空")
        
        results = await web_search_service.search_game_info(query, max_results)
        
        return {
            "query": query,
            "results": results,
            "count": len(results),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/equipment/{equipment_name}")
async def analyze_equipment_realtime(equipment_name: str):
    """实时分析装备（从网络搜索最新信息）"""
    try:
        # 搜索装备信息
        equipment_info = await web_search_service.search_specific_equipment(equipment_name)
        
        # 使用AI分析搜索结果
        analysis_prompt = f"""
基于以下搜索结果，分析装备"{equipment_name}"：

搜索结果：
{equipment_info}

请提供：
1. 装备基本信息（如果能找到）
2. 当前版本中的地位
3. 获取途径
4. 使用建议
5. 信息可信度评估

以JSON格式返回。
"""
        
        ai_analysis = await deepseek_service._make_request([
            {"role": "system", "content": "你是逆水寒游戏装备分析专家"},
            {"role": "user", "content": analysis_prompt}
        ])
        
        return {
            "equipment_name": equipment_name,
            "search_info": equipment_info,
            "ai_analysis": ai_analysis,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"实时装备分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/version")
async def get_current_version():
    """获取当前游戏版本信息"""
    try:
        version_info = await web_search_service.get_latest_version_info()
        
        return {
            "version_info": version_info,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取版本信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/meta")
async def get_current_meta():
    """获取当前meta信息"""
    try:
        meta_info = await web_search_service.monitor_meta_changes()
        
        return {
            "meta_info": meta_info,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取meta信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/knowledge")
async def get_knowledge_base():
    """获取知识库内容"""
    try:
        return {
            "knowledge_base": learning_agent.knowledge_base,
            "last_update": learning_agent.last_update.isoformat() if learning_agent.last_update else None,
            "size": len(learning_agent.knowledge_base)
        }
    except Exception as e:
        logger.error(f"获取知识库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/learn")
async def learn_from_content(
    content: str,
    source: str = "user_input",
    confidence: float = 0.5
):
    """从用户提供的内容中学习"""
    try:
        if not content:
            raise HTTPException(status_code=400, detail="内容不能为空")
        
        # 使用AI分析用户提供的内容
        analysis_prompt = f"""
分析以下逆水寒游戏相关内容，提取有用信息：

内容来源：{source}
内容：{content}

请提取：
1. 装备信息
2. 版本信息  
3. meta信息
4. 攻略建议

以JSON格式返回，并评估信息的可信度。
"""
        
        ai_analysis = await deepseek_service._make_request([
            {"role": "system", "content": "你是逆水寒游戏信息提取专家"},
            {"role": "user", "content": analysis_prompt}
        ])
        
        # 将学习到的信息添加到知识库
        if ai_analysis:
            learning_entry = {
                "content": content,
                "source": source,
                "confidence": confidence,
                "ai_analysis": ai_analysis,
                "learned_at": datetime.now().isoformat()
            }
            
            if "user_contributions" not in learning_agent.knowledge_base:
                learning_agent.knowledge_base["user_contributions"] = []
            
            learning_agent.knowledge_base["user_contributions"].append(learning_entry)
            await learning_agent.save_knowledge_base()
        
        return {
            "message": "内容学习完成",
            "analysis": ai_analysis,
            "confidence": confidence,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"学习内容失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recommendations/{profession}/{scenario}")
async def get_realtime_recommendations(
    profession: str,
    scenario: str
):
    """获取实时推荐（基于最新学习的信息）"""
    try:
        # 确保知识库是最新的
        if await learning_agent.should_update():
            await learning_agent.update_knowledge_base()
        
        # 获取推荐
        recommendations = await learning_agent.get_meta_recommendations(profession, scenario)
        
        # 搜索最新的相关信息
        search_query = f"{profession} {scenario} 推荐 构建"
        latest_info = await web_search_service.search_game_info(search_query, 5)
        
        return {
            "profession": profession,
            "scenario": scenario,
            "recommendations": recommendations,
            "latest_search_results": latest_info,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取实时推荐失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/knowledge")
async def reset_knowledge_base():
    """重置知识库（谨慎使用）"""
    try:
        learning_agent.knowledge_base = {}
        learning_agent.last_update = None
        await learning_agent.save_knowledge_base()

        return {
            "message": "知识库已重置",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"重置知识库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chat")
async def chat_with_agent(chat_data: ChatMessage):
    """与真正的AI Agent对话"""
    global real_agent

    try:
        # 强制重新初始化Agent以确保AI服务正确
        from ..services.ai_service import ai_service
        logger.info(f"全局AI服务可用性: {ai_service.is_available()}")
        logger.info(f"全局AI服务模型: {ai_service.model}")

        # 每次都重新创建Agent，确保使用最新的AI服务
        logger.info("重新创建Agent实例以确保AI服务正确")
        real_agent = RealAgentService(ai_service)
        logger.info(f"新Agent中AI服务可用性: {real_agent.ai_service.is_available()}")

        # 与Agent对话
        response = await real_agent.chat_with_user(
            chat_data.message,
            chat_data.context
        )

        return {
            "success": True,
            "agent_response": response["response"],
            "intent_analysis": {
                "intent": response["intent"],
                "confidence": response["confidence"],
                "action_taken": response["action_taken"]
            },
            "learned_something": response["learned_something"],
            "timestamp": response["timestamp"]
        }

    except Exception as e:
        logger.error(f"Agent对话失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "fallback_response": f"""
🤖 **AI Agent 系统消息**

抱歉，我遇到了技术问题：{str(e)}

**我是一个诚实的AI Agent**，我会告诉您：

关于您的问题："{chat_data.message}"

**当前状态**：
- 我正在学习逆水寒手游的真实信息
- 我不会编造装备数据或属性
- 我会承认我的知识限制

**可靠的信息来源**：
- 官方网站：https://h.163.com/
- NGA论坛：https://nga.178.com/
- 官方微博和公告

**我的承诺**：
- ✅ 诚实回答，不编造信息
- ✅ 持续学习，提升能力
- ✅ 提供可靠的信息来源
- ❌ 不会基于不确定信息给建议

请给我一些时间学习真实的游戏数据，我会成为您可信赖的游戏助手。
""",
            "timestamp": datetime.now().isoformat()
        }


@router.get("/agent/status")
async def get_agent_status():
    """获取真正的Agent状态"""
    global real_agent

    try:
        if real_agent is None:
            return {
                "agent_initialized": False,
                "message": "Agent尚未初始化，请先发送一条消息"
            }

        status = real_agent.get_agent_status()
        return {
            "agent_initialized": True,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取Agent状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/general-chat")
async def general_chat_with_agent(chat_data: ChatMessage):
    """普通聊天模式 - 结合逆水寒知识的日常对话"""
    global real_agent

    try:
        from ..services.ai_service import ai_service
        logger.info("普通聊天模式 - 重新创建Agent实例")
        real_agent = RealAgentService(ai_service)

        # 为普通聊天添加上下文提示
        enhanced_message = f"""
        用户消息: {chat_data.message}

        请以一个友好的AI助手身份回复，同时：
        1. 如果问题与逆水寒游戏相关，提供专业的游戏建议
        2. 如果是日常聊天，可以适当结合游戏元素让对话更有趣
        3. 保持自然、友好的对话风格
        4. 诚实回答，不编造信息
        """

        response = await real_agent.chat_with_user(
            enhanced_message,
            {"mode": "general", "original_message": chat_data.message}
        )

        return {
            "success": True,
            "agent_response": response["response"],
            "intent_analysis": {
                "intent": response["intent"],
                "confidence": response["confidence"],
                "action_taken": response["action_taken"]
            },
            "learned_something": response["learned_something"],
            "mode": "general",
            "timestamp": response["timestamp"]
        }

    except Exception as e:
        logger.error(f"普通聊天失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "fallback_response": "抱歉，聊天服务暂时不可用。不过我们可以聊聊逆水寒游戏相关的话题！",
            "timestamp": datetime.now().isoformat()
        }
