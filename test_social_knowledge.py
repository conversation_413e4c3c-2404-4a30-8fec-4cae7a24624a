#!/usr/bin/env python3
"""
测试社交媒体知识库功能
"""
import asyncio
import httpx
import json

async def test_social_knowledge_system():
    """测试社交知识库系统"""
    print("🔍 测试社交媒体知识库系统")
    print("=" * 60)
    
    base_url = "http://localhost:8000/api/v1/social-knowledge"
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        
        # 1. 获取支持的平台
        print("\n📱 1. 获取支持的平台")
        print("-" * 40)
        try:
            response = await client.get(f"{base_url}/platforms")
            if response.status_code == 200:
                data = response.json()
                print("✅ 平台信息获取成功")
                for platform in data["platforms"]:
                    status = "✅" if platform["api_available"] else "⚠️"
                    print(f"   {status} {platform['name']} - {platform['status']}")
                    print(f"      关键词: {', '.join(platform['keywords'])}")
            else:
                print(f"❌ 获取平台信息失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 2. 获取知识库统计
        print("\n📊 2. 获取知识库统计")
        print("-" * 40)
        try:
            response = await client.get(f"{base_url}/stats")
            if response.status_code == 200:
                data = response.json()
                stats = data["stats"]
                print("✅ 统计信息获取成功")
                print(f"   总知识条目: {stats['total_knowledge_items']}")
                print(f"   最后更新: {stats['last_update']}")
                print(f"   缓存状态: {stats['cache_status']}")
                if stats["platform_breakdown"]:
                    print("   平台分布:")
                    for platform, count in stats["platform_breakdown"].items():
                        print(f"     {platform}: {count}条")
            else:
                print(f"❌ 获取统计信息失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 3. 更新知识库
        print("\n🔄 3. 更新知识库")
        print("-" * 40)
        try:
            response = await client.post(
                f"{base_url}/update",
                json={"force_update": True}
            )
            if response.status_code == 200:
                data = response.json()
                print("✅ 知识库更新任务启动成功")
                print(f"   消息: {data['message']}")
                print(f"   预计时间: {data['estimated_time']}")
                
                # 等待一段时间让更新完成
                print("⏳ 等待知识库更新完成...")
                await asyncio.sleep(10)
                
            else:
                print(f"❌ 启动更新失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 4. 再次获取统计信息
        print("\n📊 4. 更新后的统计信息")
        print("-" * 40)
        try:
            response = await client.get(f"{base_url}/stats")
            if response.status_code == 200:
                data = response.json()
                stats = data["stats"]
                print("✅ 更新后统计信息")
                print(f"   总知识条目: {stats['total_knowledge_items']}")
                print(f"   缓存状态: {stats['cache_status']}")
                if stats["platform_breakdown"]:
                    print("   平台分布:")
                    for platform, count in stats["platform_breakdown"].items():
                        print(f"     {platform}: {count}条")
            else:
                print(f"❌ 获取统计信息失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 5. 获取热门知识
        print("\n🔥 5. 获取热门知识")
        print("-" * 40)
        try:
            response = await client.get(f"{base_url}/trending?limit=5")
            if response.status_code == 200:
                data = response.json()
                print("✅ 热门知识获取成功")
                print(f"   找到 {data['returned_count']} 条热门内容")
                
                for i, item in enumerate(data["trending_knowledge"], 1):
                    print(f"\n   📄 {i}. {item['title']}")
                    print(f"      平台: {item['platform']}")
                    print(f"      作者: {item['author']}")
                    print(f"      热度: {item['engagement']['likes']} 赞")
                    print(f"      相关性: {item['relevance_score']:.2f}")
                    if item['tags']:
                        print(f"      标签: {', '.join(item['tags'])}")
            else:
                print(f"❌ 获取热门知识失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 6. 搜索知识
        print("\n🔍 6. 搜索知识")
        print("-" * 40)
        search_queries = ["职业选择", "装备强化", "PVP技巧"]
        
        for query in search_queries:
            try:
                response = await client.post(
                    f"{base_url}/search",
                    json={"query": query, "limit": 3}
                )
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 搜索 '{query}' 成功")
                    print(f"   找到 {data['total_found']} 条相关内容")
                    
                    for i, item in enumerate(data["search_results"], 1):
                        print(f"     {i}. {item['title']} ({item['platform']})")
                else:
                    print(f"❌ 搜索 '{query}' 失败: {response.status_code}")
            except Exception as e:
                print(f"❌ 搜索异常: {e}")
        
        # 7. 获取最近知识
        print("\n⏰ 7. 获取最近知识")
        print("-" * 40)
        try:
            response = await client.get(f"{base_url}/recent?hours=24&limit=3")
            if response.status_code == 200:
                data = response.json()
                print("✅ 最近知识获取成功")
                print(f"   最近24小时新增 {data['total_recent']} 条")
                
                for i, item in enumerate(data["recent_knowledge"], 1):
                    print(f"     {i}. {item['title']} ({item['platform']})")
            else:
                print(f"❌ 获取最近知识失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 社交知识库测试完成!")
    
    print("\n💡 功能总结:")
    print("✅ 支持小红书、抖音、B站等平台")
    print("✅ 自动获取最新游戏攻略和心得")
    print("✅ 智能相关性评分和排序")
    print("✅ 支持关键词搜索")
    print("✅ 热门内容推荐")
    print("✅ 实时统计和监控")
    
    print("\n🚀 下一步建议:")
    print("1. 配置小红书和抖音的数据获取接口")
    print("2. 设置定时任务自动更新知识库")
    print("3. 优化内容质量评估算法")
    print("4. 集成到AI Agent的回复生成中")

async def test_agent_with_social_knowledge():
    """测试Agent结合社交知识库的回复"""
    print("\n🤖 测试Agent结合社交知识库")
    print("=" * 60)
    
    # 这里可以测试Agent如何利用社交知识库来增强回复
    # 暂时先显示概念
    print("💡 概念演示:")
    print("当用户询问'新手职业推荐'时，Agent将:")
    print("1. 从社交知识库搜索相关内容")
    print("2. 结合DeepSeek-V3的分析能力")
    print("3. 生成基于真实玩家经验的回复")
    print("4. 引用具体的攻略来源")

async def main():
    """主测试函数"""
    await test_social_knowledge_system()
    await test_agent_with_social_knowledge()

if __name__ == "__main__":
    asyncio.run(main())
