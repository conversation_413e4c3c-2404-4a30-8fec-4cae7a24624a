import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Tag, 
  Space, 
  Button, 
  Select, 
  Input,
  Typography,
  Tooltip
} from 'antd'
import { 
  UnorderedListOutlined, 
  SearchOutlined,
  EyeOutlined,
  BarChartOutlined
} from '@ant-design/icons'

const { Title } = Typography
const { Option } = Select
const { Search } = Input

interface Equipment {
  id: string
  name: string
  type: string
  level: number
  quality: string
  attributes: Array<{
    type: string
    value: number
    is_percentage: boolean
  }>
  source?: string
}

const EquipmentList: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [equipment, setEquipment] = useState<Equipment[]>([])
  const [filteredEquipment, setFilteredEquipment] = useState<Equipment[]>([])

  // 模拟装备数据
  const mockEquipment: Equipment[] = [
    {
      id: 'sword_001',
      name: '烈焰长剑',
      type: 'weapon',
      level: 50,
      quality: '紫色',
      attributes: [
        { type: 'attack', value: 1200, is_percentage: false },
        { type: 'crit_rate', value: 15, is_percentage: true }
      ],
      source: '烈焰副本'
    },
    {
      id: 'sword_002',
      name: '寒冰剑',
      type: 'weapon',
      level: 55,
      quality: '橙色',
      attributes: [
        { type: 'attack', value: 1450, is_percentage: false },
        { type: 'crit_rate', value: 18, is_percentage: true }
      ],
      source: '寒冰洞窟'
    },
    {
      id: 'armor_001',
      name: '玄铁护甲',
      type: 'armor',
      level: 50,
      quality: '紫色',
      attributes: [
        { type: 'defense', value: 800, is_percentage: false },
        { type: 'hp', value: 5000, is_percentage: false }
      ],
      source: '铁匠铺'
    }
  ]

  useEffect(() => {
    setEquipment(mockEquipment)
    setFilteredEquipment(mockEquipment)
  }, [])

  const getQualityColor = (quality: string) => {
    const colors: Record<string, string> = {
      '白色': 'default',
      '绿色': 'success',
      '蓝色': 'processing',
      '紫色': 'warning',
      '橙色': 'error'
    }
    return colors[quality] || 'default'
  }

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'weapon': '武器',
      'armor': '护甲',
      'accessory': '饰品',
      'ring': '戒指',
      'necklace': '项链',
      'bracelet': '手镯'
    }
    return labels[type] || type
  }

  const getAttributeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'attack': '攻击力',
      'defense': '防御力',
      'hp': '生命值',
      'mp': '内力值',
      'crit_rate': '暴击率',
      'crit_damage': '暴击伤害',
      'speed': '速度'
    }
    return labels[type] || type
  }

  const columns = [
    {
      title: '装备名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Equipment) => (
        <Space>
          <span style={{ fontWeight: 'bold' }}>{text}</span>
          <Tag color={getQualityColor(record.quality)}>{record.quality}</Tag>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => getTypeLabel(type)
    },
    {
      title: '等级',
      dataIndex: 'level',
      key: 'level',
      sorter: (a: Equipment, b: Equipment) => a.level - b.level
    },
    {
      title: '主要属性',
      dataIndex: 'attributes',
      key: 'attributes',
      render: (attributes: Equipment['attributes']) => (
        <Space direction="vertical" size="small">
          {attributes.slice(0, 2).map((attr, index) => (
            <span key={index} style={{ fontSize: '12px' }}>
              {getAttributeLabel(attr.type)}: {attr.value}{attr.is_percentage ? '%' : ''}
            </span>
          ))}
          {attributes.length > 2 && (
            <Tooltip title={
              attributes.slice(2).map(attr => 
                `${getAttributeLabel(attr.type)}: ${attr.value}${attr.is_percentage ? '%' : ''}`
              ).join('\n')
            }>
              <span style={{ color: '#1890ff', cursor: 'pointer' }}>
                +{attributes.length - 2} 更多属性
              </span>
            </Tooltip>
          )}
        </Space>
      )
    },
    {
      title: '获取途径',
      dataIndex: 'source',
      key: 'source'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: Equipment) => (
        <Space>
          <Button 
            type="text" 
            icon={<EyeOutlined />}
            onClick={() => console.log('查看详情:', record.id)}
          >
            详情
          </Button>
          <Button 
            type="text" 
            icon={<BarChartOutlined />}
            onClick={() => console.log('分析装备:', record.id)}
          >
            分析
          </Button>
        </Space>
      )
    }
  ]

  const handleSearch = (value: string) => {
    const filtered = equipment.filter(item => 
      item.name.toLowerCase().includes(value.toLowerCase()) ||
      item.source?.toLowerCase().includes(value.toLowerCase())
    )
    setFilteredEquipment(filtered)
  }

  const handleTypeFilter = (type: string) => {
    if (type === 'all') {
      setFilteredEquipment(equipment)
    } else {
      const filtered = equipment.filter(item => item.type === type)
      setFilteredEquipment(filtered)
    }
  }

  return (
    <div className="fade-in">
      <Card title={
        <Space>
          <UnorderedListOutlined />
          装备库
        </Space>
      }>
        {/* 筛选工具栏 */}
        <Space style={{ marginBottom: 16 }}>
          <Search
            placeholder="搜索装备名称或获取途径"
            allowClear
            onSearch={handleSearch}
            style={{ width: 300 }}
            enterButton={<SearchOutlined />}
          />
          
          <Select
            defaultValue="all"
            style={{ width: 120 }}
            onChange={handleTypeFilter}
          >
            <Option value="all">全部类型</Option>
            <Option value="weapon">武器</Option>
            <Option value="armor">护甲</Option>
            <Option value="accessory">饰品</Option>
          </Select>
        </Space>

        {/* 装备表格 */}
        <Table
          columns={columns}
          dataSource={filteredEquipment}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 件装备`
          }}
        />
      </Card>
    </div>
  )
}

export default EquipmentList
