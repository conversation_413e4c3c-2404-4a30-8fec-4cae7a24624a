#!/usr/bin/env python3
"""
测试超时优化后的Agent性能
"""
import asyncio
import httpx
import time

async def test_timeout_optimization():
    """测试超时优化"""
    print("🕐 测试超时优化后的Agent性能")
    print("=" * 60)
    
    async with httpx.AsyncClient(timeout=90.0) as client:  # 90秒超时
        
        # 测试不同复杂度的问题
        test_cases = [
            {
                "name": "简单问候",
                "message": "你好",
                "expected_time": "< 10秒"
            },
            {
                "name": "中等复杂问题", 
                "message": "请详细介绍逆水寒手游的职业系统",
                "expected_time": "< 30秒"
            },
            {
                "name": "复杂分析问题",
                "message": "请分析当前版本最强的PVP装备搭配，并说明原因",
                "expected_time": "< 60秒"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}: {test_case['name']}")
            print(f"问题: {test_case['message']}")
            print(f"预期时间: {test_case['expected_time']}")
            print("-" * 50)
            
            start_time = time.time()
            
            try:
                response = await client.post(
                    "http://localhost:8000/api/v1/learning/chat",
                    json={"message": test_case["message"]}
                )
                
                end_time = time.time()
                elapsed_time = end_time - start_time
                
                print(f"⏱️  实际耗时: {elapsed_time:.2f}秒")
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data.get("success", False):
                        print("✅ 回复成功!")
                        
                        # 显示回复摘要
                        response_text = data["agent_response"]
                        if len(response_text) > 100:
                            print(f"📄 回复摘要: {response_text[:100]}...")
                        else:
                            print(f"📄 完整回复: {response_text}")
                            
                        # 性能评估
                        if elapsed_time < 10:
                            print("🚀 性能: 优秀")
                        elif elapsed_time < 30:
                            print("⚡ 性能: 良好")
                        elif elapsed_time < 60:
                            print("⏳ 性能: 可接受")
                        else:
                            print("🐌 性能: 需要优化")
                            
                    else:
                        print("❌ 回复失败")
                        if "error" in data:
                            print(f"   错误: {data['error']}")
                        
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    
            except asyncio.TimeoutError:
                end_time = time.time()
                elapsed_time = end_time - start_time
                print(f"⏱️  超时时间: {elapsed_time:.2f}秒")
                print("⚠️  请求超时 - 需要进一步优化")
                
            except Exception as e:
                end_time = time.time()
                elapsed_time = end_time - start_time
                print(f"⏱️  异常时间: {elapsed_time:.2f}秒")
                print(f"❌ 请求异常: {e}")
            
            print("\n" + "=" * 60)
            
            # 测试间隔，避免频繁请求
            if i < len(test_cases):
                print("⏸️  等待5秒后进行下一个测试...")
                await asyncio.sleep(5)
    
    print("\n🎯 超时优化总结:")
    print("✅ AI服务超时: 60秒")
    print("✅ 连接测试超时: 30秒") 
    print("✅ 客户端超时: 90秒")
    print("✅ 重试机制: 3次")
    print("\n💡 建议:")
    print("- 简单问题应在10秒内完成")
    print("- 复杂分析应在60秒内完成")
    print("- 如果仍有超时，可考虑进一步增加超时时间")

async def test_concurrent_requests():
    """测试并发请求性能"""
    print("\n🔄 测试并发请求性能")
    print("=" * 60)
    
    async def single_request(client, message, request_id):
        """单个请求"""
        start_time = time.time()
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/learning/chat",
                json={"message": f"{message} (请求{request_id})"}
            )
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                success = data.get("success", False)
                return {
                    "request_id": request_id,
                    "success": success,
                    "time": elapsed_time,
                    "status": "成功" if success else "失败"
                }
            else:
                return {
                    "request_id": request_id,
                    "success": False,
                    "time": elapsed_time,
                    "status": f"HTTP错误{response.status_code}"
                }
        except Exception as e:
            end_time = time.time()
            elapsed_time = end_time - start_time
            return {
                "request_id": request_id,
                "success": False,
                "time": elapsed_time,
                "status": f"异常: {str(e)[:50]}"
            }
    
    # 发送3个并发请求
    async with httpx.AsyncClient(timeout=90.0) as client:
        tasks = []
        for i in range(3):
            task = single_request(client, "你好，请简单介绍一下自己", i+1)
            tasks.append(task)
        
        print("🚀 发送3个并发请求...")
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print("\n📊 结果统计:")
        
        success_count = 0
        for result in results:
            if isinstance(result, dict):
                print(f"   请求{result['request_id']}: {result['status']} ({result['time']:.2f}秒)")
                if result['success']:
                    success_count += 1
            else:
                print(f"   请求异常: {result}")
        
        print(f"\n✅ 成功率: {success_count}/3 ({success_count/3*100:.1f}%)")
        
        if success_count == 3:
            print("🎉 并发性能优秀!")
        elif success_count >= 2:
            print("👍 并发性能良好")
        else:
            print("⚠️  并发性能需要改进")

async def main():
    """主测试函数"""
    await test_timeout_optimization()
    await test_concurrent_requests()
    
    print("\n" + "=" * 60)
    print("🎊 超时优化测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
