#!/usr/bin/env python3
"""
社交媒体知识库API
管理从小红书、抖音等平台获取的游戏知识
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from ..services.social_knowledge_service import social_knowledge_service

router = APIRouter()

class KnowledgeUpdateRequest(BaseModel):
    """知识库更新请求"""
    platforms: Optional[List[str]] = None  # 指定更新的平台
    force_update: bool = False  # 强制更新

class KnowledgeSearchRequest(BaseModel):
    """知识搜索请求"""
    query: str
    category: Optional[str] = "all"
    limit: int = 20

@router.post("/update")
async def update_social_knowledge(
    request: KnowledgeUpdateRequest,
    background_tasks: BackgroundTasks
):
    """更新社交媒体知识库"""
    try:
        logger.info("🔄 开始更新社交媒体知识库...")
        
        # 在后台执行更新任务
        background_tasks.add_task(
            social_knowledge_service.update_knowledge_from_social_media
        )
        
        return {
            "success": True,
            "message": "知识库更新任务已启动，将在后台执行",
            "estimated_time": "2-5分钟",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"启动知识库更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/force-update")
async def force_update_all_platforms(background_tasks: BackgroundTasks):
    """强制更新所有平台的最新数据"""
    try:
        logger.info("🚀 强制更新所有平台数据...")

        # 立即执行更新任务
        background_tasks.add_task(
            social_knowledge_service.update_knowledge_from_social_media
        )

        # 同时启动学习Agent更新
        from ..services.learning_agent import learning_agent
        background_tasks.add_task(
            learning_agent.update_knowledge_base
        )

        return {
            "success": True,
            "message": "强制更新任务已启动，正在获取最新数据",
            "platforms": ["小红书", "抖音", "B站"],
            "estimated_time": "3-8分钟",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"强制更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/debug/cache")
async def debug_knowledge_cache():
    """调试：查看知识库缓存内容"""
    try:
        cache_info = {}
        total_items = 0

        for cache_key, items in social_knowledge_service.knowledge_cache.items():
            cache_info[cache_key] = {
                "count": len(items),
                "sample_titles": [item.get("title", "")[:50] for item in items[:3]]
            }
            total_items += len(items)

        return {
            "success": True,
            "total_items": total_items,
            "cache_breakdown": cache_info,
            "last_update": social_knowledge_service.last_update,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取缓存信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/trending")
async def get_trending_knowledge(category: str = "all", limit: int = 20):
    """获取热门知识"""
    try:
        trending_items = await social_knowledge_service.get_trending_knowledge(category)
        
        # 限制返回数量
        limited_items = trending_items[:limit]
        
        return {
            "success": True,
            "trending_knowledge": limited_items,
            "total_count": len(trending_items),
            "returned_count": len(limited_items),
            "category": category,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取热门知识失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search")
async def search_knowledge(request: KnowledgeSearchRequest):
    """搜索知识库"""
    try:
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="搜索关键词不能为空")
        
        search_results = await social_knowledge_service.search_knowledge(request.query)
        
        # 限制返回数量
        limited_results = search_results[:request.limit]
        
        return {
            "success": True,
            "query": request.query,
            "search_results": limited_results,
            "total_found": len(search_results),
            "returned_count": len(limited_results),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索知识库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_knowledge_stats():
    """获取知识库统计信息"""
    try:
        stats = social_knowledge_service.get_knowledge_stats()
        
        return {
            "success": True,
            "stats": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取知识库统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/platforms")
async def get_supported_platforms():
    """获取支持的平台列表"""
    try:
        platforms_info = []
        
        for platform_id, platform_data in social_knowledge_service.platforms.items():
            platforms_info.append({
                "id": platform_id,
                "name": platform_data["name"],
                "keywords": platform_data["keywords"],
                "api_available": platform_data["api_available"],
                "priority": platform_data["priority"],
                "status": "可用" if platform_data["api_available"] else "需要配置"
            })
        
        return {
            "success": True,
            "platforms": platforms_info,
            "total_platforms": len(platforms_info),
            "available_platforms": len([p for p in platforms_info if p["api_available"]]),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取平台信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/recent")
async def get_recent_knowledge(hours: int = 24, limit: int = 10):
    """获取最近的知识更新"""
    try:
        # 获取所有知识
        all_knowledge = []
        for cache_key, items in social_knowledge_service.knowledge_cache.items():
            all_knowledge.extend(items)
        
        # 按提取时间排序
        recent_knowledge = sorted(
            all_knowledge,
            key=lambda x: x.get("extracted_time", datetime.min),
            reverse=True
        )
        
        # 过滤最近N小时的内容
        cutoff_time = datetime.now() - timedelta(hours=hours)
        filtered_knowledge = [
            item for item in recent_knowledge
            if item.get("extracted_time", datetime.min) > cutoff_time
        ]
        
        return {
            "success": True,
            "recent_knowledge": filtered_knowledge[:limit],
            "total_recent": len(filtered_knowledge),
            "returned_count": min(len(filtered_knowledge), limit),
            "time_range_hours": hours,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取最近知识失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cache")
async def clear_knowledge_cache():
    """清空知识库缓存"""
    try:
        old_count = sum(len(items) for items in social_knowledge_service.knowledge_cache.values())
        
        # 清空缓存
        social_knowledge_service.knowledge_cache.clear()
        social_knowledge_service.last_update.clear()
        
        logger.info(f"知识库缓存已清空，删除了{old_count}条记录")
        
        return {
            "success": True,
            "message": f"知识库缓存已清空，删除了{old_count}条记录",
            "cleared_count": old_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"清空知识库缓存失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 导入必要的模块
from datetime import timedelta
