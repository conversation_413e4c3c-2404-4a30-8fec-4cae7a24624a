/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 装备品质颜色 */
.quality-white { color: #9d9d9d; }
.quality-green { color: #1eff00; }
.quality-blue { color: #0070dd; }
.quality-purple { color: #a335ee; }
.quality-orange { color: #ff8000; }

/* 装备品质背景 */
.quality-bg-white { background-color: rgba(157, 157, 157, 0.1); }
.quality-bg-green { background-color: rgba(30, 255, 0, 0.1); }
.quality-bg-blue { background-color: rgba(0, 112, 221, 0.1); }
.quality-bg-purple { background-color: rgba(163, 53, 238, 0.1); }
.quality-bg-orange { background-color: rgba(255, 128, 0, 0.1); }
