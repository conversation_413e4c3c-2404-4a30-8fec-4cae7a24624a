{"dungeons": [{"id": "shenlin_hero", "name": "深林团本·英雄难度", "level_requirement": 80, "difficulty": "英雄", "boss_info": {"name": "森林守护者·古树王", "hp": 8500000, "main_attributes": {"fire_resist": 60, "ice_resist": 20, "thunder_resist": 40, "poison_resist": 80}, "weakness": ["fire", "thunder"], "special_mechanics": ["根须缠绕：需要高闪避率", "毒雾爆发：需要毒抗60%+", "愤怒模式：需要高爆发输出"]}, "recommended_equipment": {"priority_attributes": [{"type": "fire_damage", "weight": 1.0}, {"type": "thunder_damage", "weight": 1.0}, {"type": "poison_resist", "weight": 0.9}, {"type": "dodge_rate", "weight": 0.8}, {"type": "crit_damage", "weight": 0.7}], "profession_strategies": {"swordsman": {"role": "主坦克", "key_attributes": ["defense", "hp", "poison_resist", "dodge_rate"], "equipment_focus": "生存为主，适量输出"}, "archer": {"role": "远程输出", "key_attributes": ["attack", "crit_damage", "fire_damage", "hit_rate"], "equipment_focus": "最大化火系伤害输出"}, "mage": {"role": "法术输出", "key_attributes": ["attack", "element_penetration", "fire_damage", "mp"], "equipment_focus": "火系法术伤害和续航"}, "assassin": {"role": "爆发输出", "key_attributes": ["crit_rate", "crit_damage", "dodge_rate", "speed"], "equipment_focus": "高爆发和生存能力"}, "monk": {"role": "副坦克", "key_attributes": ["hp", "defense", "poison_resist", "damage_reduction"], "equipment_focus": "极限生存和团队保护"}, "doctor": {"role": "治疗支援", "key_attributes": ["recovery", "mp", "poison_resist", "heal_bonus"], "equipment_focus": "治疗效果和自身生存"}}}, "rewards": ["百炼套装", "龙魂戒", "高级强化石"]}, {"id": "dongting_hero", "name": "洞庭问剑·英雄难度", "level_requirement": 85, "difficulty": "英雄", "boss_info": {"name": "剑圣·独孤求败", "hp": 12000000, "main_attributes": {"attack": 15000, "crit_rate": 45, "crit_damage": 80, "speed": 180}, "weakness": ["控制技能", "远程攻击"], "special_mechanics": ["剑气纵横：需要高闪避或格挡", "独孤九剑：需要控制技能打断", "剑心通明：免疫所有负面状态"]}, "recommended_equipment": {"priority_attributes": [{"type": "dodge_rate", "weight": 1.0}, {"type": "control_resist", "weight": 0.9}, {"type": "hit_rate", "weight": 0.8}, {"type": "crit_damage", "weight": 0.8}, {"type": "speed", "weight": 0.7}], "profession_strategies": {"swordsman": {"role": "格挡坦克", "key_attributes": ["defense", "dodge_rate", "control_resist", "hp"], "equipment_focus": "格挡和闪避并重"}, "archer": {"role": "远程风筝", "key_attributes": ["attack", "hit_rate", "speed", "crit_damage"], "equipment_focus": "远程输出和机动性"}, "mage": {"role": "控制输出", "key_attributes": ["control_power", "element_penetration", "mp", "speed"], "equipment_focus": "控制技能和法术输出"}, "assassin": {"role": "机动输出", "key_attributes": ["speed", "dodge_rate", "crit_rate", "crit_damage"], "equipment_focus": "极限机动和爆发"}, "monk": {"role": "控制坦克", "key_attributes": ["control_power", "hp", "defense", "speed"], "equipment_focus": "控制技能和生存"}, "doctor": {"role": "远程支援", "key_attributes": ["heal_bonus", "speed", "mp", "dodge_rate"], "equipment_focus": "治疗和自保能力"}}}, "rewards": ["天地·破云弓", "独珍装备", "剑圣传承"]}, {"id": "zhaixing_normal", "name": "摘星宫副本", "level_requirement": 75, "difficulty": "普通", "boss_info": {"name": "星宿老仙", "hp": 6000000, "main_attributes": {"element_damage": 12000, "element_penetration": 8000, "mp": 50000, "recovery": 60}, "weakness": ["物理攻击", "元素抗性"], "special_mechanics": ["星辰坠落：范围法术攻击", "星光护盾：需要物理攻击破盾", "星宿轮转：随机改变元素属性"]}, "recommended_equipment": {"priority_attributes": [{"type": "element_resist", "weight": 1.0}, {"type": "attack", "weight": 0.9}, {"type": "magic_shield", "weight": 0.8}, {"type": "hp", "weight": 0.7}, {"type": "speed", "weight": 0.6}], "profession_strategies": {"swordsman": {"role": "破盾主力", "key_attributes": ["attack", "element_resist", "hp", "crit_rate"], "equipment_focus": "物理输出和法术防护"}, "archer": {"role": "持续输出", "key_attributes": ["attack", "hit_rate", "element_resist", "speed"], "equipment_focus": "稳定物理输出"}, "mage": {"role": "元素对抗", "key_attributes": ["element_resist", "element_penetration", "mp", "magic_shield"], "equipment_focus": "法术防护和反制"}, "assassin": {"role": "机动输出", "key_attributes": ["speed", "dodge_rate", "attack", "element_resist"], "equipment_focus": "机动性和生存"}, "monk": {"role": "团队保护", "key_attributes": ["hp", "element_resist", "damage_reduction", "team_buff"], "equipment_focus": "团队增益和防护"}, "doctor": {"role": "法术治疗", "key_attributes": ["heal_bonus", "mp", "element_resist", "recovery"], "equipment_focus": "治疗和法术防护"}}}, "rewards": ["玄机·星河法杖", "星辰法袍", "星耀项链"]}], "general_pve_strategies": {"equipment_priorities": {"tank_roles": ["hp", "defense", "damage_reduction", "resist_all"], "dps_roles": ["attack", "crit_rate", "crit_damage", "penetration"], "support_roles": ["recovery", "mp", "heal_bonus", "team_buff"], "survival_focus": ["dodge_rate", "speed", "resist_specific", "hp"]}, "attribute_weights": {"pve_general": {"attack": 0.9, "hp": 0.8, "defense": 0.7, "crit_damage": 0.8, "element_resist": 0.6, "recovery": 0.5}}}}