import React, { useState } from 'react'
import { 
  Card, 
  Form, 
  Select, 
  Button, 
  Space, 
  Typography, 
  Alert,
  Spin,
  Progress,
  Tag,
  Divider
} from 'antd'
import { BarChartOutlined, ThunderboltOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography
const { Option } = Select

interface AnalysisResult {
  total_score: number
  attribute_scores: Record<string, number>
  strengths: string[]
  weaknesses: string[]
  recommendations: string[]
}

const EquipmentAnalysis: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<AnalysisResult | null>(null)

  const professions = [
    { value: 'swordsman', label: '剑客' },
    { value: 'archer', label: '弓手' },
    { value: 'mage', label: '法师' },
    { value: 'assassin', label: '刺客' },
    { value: 'monk', label: '武僧' },
    { value: 'doctor', label: '医师' }
  ]

  const scenarios = [
    { value: 'pvp', label: 'PVP (玩家对战)' },
    { value: 'pve', label: 'PVE (副本刷怪)' }
  ]

  const equipmentList = [
    { value: 'sword_001', label: '烈焰长剑 (Lv.50 紫色)' },
    { value: 'sword_002', label: '寒冰剑 (Lv.55 橙色)' },
    { value: 'bow_001', label: '疾风弓 (Lv.48 紫色)' },
    { value: 'staff_001', label: '雷鸣法杖 (Lv.52 橙色)' },
    { value: 'dagger_001', label: '影刃 (Lv.45 紫色)' },
    { value: 'hammer_001', label: '破山锤 (Lv.60 橙色)' }
  ]

  const handleAnalyze = async (values: any) => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 模拟分析结果
      const mockResult: AnalysisResult = {
        total_score: 85.5,
        attribute_scores: {
          attack: 90,
          defense: 70,
          crit_rate: 85,
          crit_damage: 88,
          speed: 75
        },
        strengths: [
          '攻击力出色，适合输出型玩家',
          '暴击属性优秀，爆发能力强',
          '整体属性均衡，适应性好'
        ],
        weaknesses: [
          '防御力相对较低，生存能力有限',
          '缺乏抗性属性，面对特殊伤害时脆弱'
        ],
        recommendations: [
          '建议搭配防御型装备提升生存能力',
          '可考虑强化抗性属性',
          '适合与治疗职业组队'
        ]
      }
      
      setResult(mockResult)
    } catch (error) {
      console.error('分析失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return '#52c41a'
    if (score >= 80) return '#1890ff'
    if (score >= 70) return '#faad14'
    if (score >= 60) return '#fa8c16'
    return '#f5222d'
  }

  const getScoreStatus = (score: number) => {
    if (score >= 90) return 'success'
    if (score >= 70) return 'normal'
    if (score >= 50) return 'active'
    return 'exception'
  }

  return (
    <div className="fade-in">
      <Card title={
        <Space>
          <BarChartOutlined />
          装备智能分析
        </Space>
      }>
        <Alert
          message="AI装备分析"
          description="基于DeepSeek R1模型，结合职业特点和使用场景，为您的装备提供专业评分和建议"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={handleAnalyze}
        >
          <Form.Item
            name="equipment"
            label="选择装备"
            rules={[{ required: true, message: '请选择要分析的装备' }]}
          >
            <Select placeholder="请选择装备" size="large">
              {equipmentList.map(item => (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="profession"
            label="职业类型"
            rules={[{ required: true, message: '请选择职业' }]}
          >
            <Select placeholder="请选择职业" size="large">
              {professions.map(item => (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="scenario"
            label="使用场景"
            rules={[{ required: true, message: '请选择使用场景' }]}
          >
            <Select placeholder="请选择使用场景" size="large">
              {scenarios.map(item => (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              size="large"
              loading={loading}
              icon={<ThunderboltOutlined />}
              block
            >
              {loading ? '分析中...' : '开始分析'}
            </Button>
          </Form.Item>
        </Form>

        {result && (
          <Card 
            title="分析结果" 
            style={{ marginTop: 24 }}
            extra={<Tag color="blue">AI分析报告</Tag>}
          >
            {/* 总评分 */}
            <div style={{ textAlign: 'center', marginBottom: 24 }}>
              <Title level={2} style={{ color: getScoreColor(result.total_score) }}>
                {result.total_score}分
              </Title>
              <Progress
                type="circle"
                percent={result.total_score}
                status={getScoreStatus(result.total_score)}
                strokeColor={getScoreColor(result.total_score)}
              />
            </div>

            <Divider />

            {/* 属性评分 */}
            <Title level={4}>属性评分</Title>
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(result.attribute_scores).map(([attr, score]) => (
                <div key={attr} style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{ width: 80, display: 'inline-block' }}>
                    {attr === 'attack' ? '攻击力' :
                     attr === 'defense' ? '防御力' :
                     attr === 'crit_rate' ? '暴击率' :
                     attr === 'crit_damage' ? '暴击伤害' :
                     attr === 'speed' ? '速度' : attr}:
                  </span>
                  <Progress
                    percent={score}
                    strokeColor={getScoreColor(score)}
                    style={{ flex: 1, marginLeft: 16 }}
                  />
                  <span style={{ marginLeft: 8, minWidth: 40 }}>{score}分</span>
                </div>
              ))}
            </Space>

            <Divider />

            {/* 优势分析 */}
            <Title level={4}>💪 优势分析</Title>
            <ul>
              {result.strengths.map((strength, index) => (
                <li key={index}>
                  <Paragraph style={{ margin: 0 }}>{strength}</Paragraph>
                </li>
              ))}
            </ul>

            <Divider />

            {/* 劣势分析 */}
            <Title level={4}>⚠️ 劣势分析</Title>
            <ul>
              {result.weaknesses.map((weakness, index) => (
                <li key={index}>
                  <Paragraph style={{ margin: 0 }}>{weakness}</Paragraph>
                </li>
              ))}
            </ul>

            <Divider />

            {/* 使用建议 */}
            <Title level={4}>💡 使用建议</Title>
            <ul>
              {result.recommendations.map((recommendation, index) => (
                <li key={index}>
                  <Paragraph style={{ margin: 0 }}>{recommendation}</Paragraph>
                </li>
              ))}
            </ul>
          </Card>
        )}
      </Card>
    </div>
  )
}

export default EquipmentAnalysis
