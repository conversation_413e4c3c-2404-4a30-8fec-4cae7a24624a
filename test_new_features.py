#!/usr/bin/env python3
"""
测试新的内置装备数据库功能
"""
import asyncio
import httpx
import json

async def test_new_features():
    """测试新功能"""
    print("🔍 测试新的内置装备数据库功能...")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        
        # 1. 测试搜索百炼装备
        print("1️⃣ 搜索百炼装备:")
        try:
            response = await client.get("http://localhost:8000/api/v1/learning/search?query=百炼")
            if response.status_code == 200:
                data = response.json()
                print(f"   找到 {data['count']} 个结果:")
                for result in data['results']:
                    print(f"   • {result['title']}")
                    print(f"     描述: {result['description']}")
                    print(f"     来源: {result['source']}")
                    print()
            else:
                print(f"   ❌ 搜索失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 搜索异常: {e}")
        
        # 2. 测试装备分析
        print("2️⃣ 分析具体装备:")
        equipment_names = ["百炼·龙吟剑", "潮光·海珠法杖", "不存在的装备"]
        
        for equipment_name in equipment_names:
            print(f"\n   分析: {equipment_name}")
            try:
                response = await client.get(f"http://localhost:8000/api/v1/learning/equipment/{equipment_name}")
                if response.status_code == 200:
                    data = response.json()
                    
                    if data['found']:
                        eq_data = data['equipment_data']
                        analysis = data['analysis']
                        print(f"   ✅ 找到装备!")
                        print(f"      品质: {eq_data['quality']}")
                        print(f"      等级: {eq_data['level']}")
                        print(f"      评分: {analysis['score']}/100 ({analysis['rating']}级)")
                        print(f"      获取: {eq_data['obtain_method']}")
                        
                        # 显示详细分析的前几行
                        detailed = data['detailed_analysis'].split('\n')[:5]
                        print(f"      分析预览: {detailed[0]}")
                    else:
                        print(f"   ⚠️ 未找到装备")
                        print(f"      消息: {data['message']}")
                        suggestions = data.get('suggestions', [])
                        if suggestions:
                            print(f"      建议: {suggestions[0]['name']} ({suggestions[0]['reason']})")
                else:
                    print(f"   ❌ 分析失败: {response.status_code}")
            except Exception as e:
                print(f"   ❌ 分析异常: {e}")
        
        # 3. 测试搜索武器
        print("\n3️⃣ 搜索武器类装备:")
        try:
            response = await client.get("http://localhost:8000/api/v1/learning/search?query=武器")
            if response.status_code == 200:
                data = response.json()
                print(f"   找到 {data['count']} 个武器:")
                for result in data['results']:
                    if result['type'] == 'equipment':
                        eq_data = result['data']
                        print(f"   • {eq_data['name']} - {eq_data['quality']} ({eq_data['rating']}级)")
            else:
                print(f"   ❌ 搜索失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 搜索异常: {e}")
        
        # 4. 测试搜索不存在的装备
        print("\n4️⃣ 搜索不存在的装备:")
        try:
            response = await client.get("http://localhost:8000/api/v1/learning/search?query=神器装备")
            if response.status_code == 200:
                data = response.json()
                print(f"   搜索结果: {data['count']} 个")
                for result in data['results']:
                    print(f"   • {result['title']} (类型: {result['type']})")
            else:
                print(f"   ❌ 搜索失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 搜索异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")
    print("\n💡 新功能特点:")
    print("✅ 内置装备数据库 - 不再依赖外部搜索")
    print("✅ 详细装备分析 - 包含属性、评分、建议")
    print("✅ 智能搜索建议 - 找不到时提供相关推荐")
    print("✅ 专业评级系统 - S/A/B/C/D 等级评定")
    print("✅ 获取途径指导 - 明确告知如何获得装备")

async def main():
    await test_new_features()

if __name__ == "__main__":
    asyncio.run(main())
