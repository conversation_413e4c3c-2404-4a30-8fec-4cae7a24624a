#!/usr/bin/env python3
"""
测试系统改进效果
"""
import asyncio
import httpx

async def test_improvements():
    """测试系统改进"""
    print("测试系统改进效果")
    print("=" * 50)
    
    test_cases = [
        "你好，你是什么模型？",
        "推荐一个新手职业",
        "碎梦PVP怎么玩？",
        "逆水寒有哪些装备类型？"
    ]
    
    async with httpx.AsyncClient(timeout=90.0) as client:
        for i, question in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {question}")
            print("-" * 40)
            
            try:
                response = await client.post(
                    "http://localhost:8000/api/v1/learning/chat",
                    json={"message": question}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        print("成功")
                        reply = data["agent_response"]
                        if len(reply) > 200:
                            print(f"回复: {reply[:200]}...")
                        else:
                            print(f"回复: {reply}")
                        
                        # 检查意图分析
                        intent = data.get("intent_analysis", {}).get("intent", {})
                        if "primary_intent" in intent:
                            print(f"意图: {intent['primary_intent']}")
                        
                    else:
                        print("失败")
                        print(f"错误: {data.get('error', '未知错误')}")
                else:
                    print(f"HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"异常: {e}")
            
            await asyncio.sleep(3)
    
    print("\n改进测试完成!")

async def test_social_knowledge_integration():
    """测试社交知识库集成"""
    print("\n测试社交知识库集成")
    print("=" * 50)
    
    # 先更新知识库
    async with httpx.AsyncClient(timeout=30.0) as client:
        print("更新社交知识库...")
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/social-knowledge/update",
                json={"force_update": True}
            )
            if response.status_code == 200:
                print("知识库更新任务启动成功")
                await asyncio.sleep(10)  # 等待更新完成
            else:
                print(f"更新失败: {response.status_code}")
        except Exception as e:
            print(f"更新异常: {e}")
        
        # 测试Agent是否能利用社交知识库
        print("\n测试Agent利用社交知识库...")
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/learning/chat",
                json={"message": "新手职业选择有什么建议？"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    reply = data["agent_response"]
                    print("Agent回复:")
                    print(reply[:300] + "..." if len(reply) > 300 else reply)
                    
                    # 检查是否包含社交媒体内容
                    if "小红书" in reply or "抖音" in reply or "来源" in reply:
                        print("\n检测到社交知识库集成成功!")
                    else:
                        print("\n未检测到明显的社交知识库集成")
                else:
                    print(f"Agent回复失败: {data.get('error')}")
            else:
                print(f"请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"测试异常: {e}")

async def main():
    """主测试函数"""
    await test_improvements()
    await test_social_knowledge_integration()
    
    print("\n" + "=" * 50)
    print("系统改进测试完成!")
    
    print("\n改进总结:")
    print("1. 修复了编码问题")
    print("2. 增强了意图解析的兼容性")
    print("3. 优化了AI提示词质量")
    print("4. 集成了社交知识库")
    print("5. 提升了回复的专业性")

if __name__ == "__main__":
    asyncio.run(main())
