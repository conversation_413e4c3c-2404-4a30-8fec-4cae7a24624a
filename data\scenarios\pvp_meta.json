{"pvp_modes": [{"id": "jingji_arena", "name": "竞技场", "mode_type": "1v1", "level_requirement": 60, "current_meta": {"tier_s_builds": [{"profession": "assassin", "build_name": "暴击刺客", "core_equipment": ["血魅·噬魂刃", "影舞·夜行衣", "疾风·敏捷戒指"], "key_attributes": {"crit_rate": 35, "crit_damage": 60, "speed": 25, "dodge_rate": 30}, "strategy": "高机动性，一套秒杀", "counters": ["高血量坦克", "控制法师"], "win_rate": 78}, {"profession": "archer", "build_name": "风筝弓手", "core_equipment": ["流星·追月弓", "风影·疾行衣", "天命·幸运戒指"], "key_attributes": {"attack": 2500, "hit_rate": 25, "speed": 20, "crit_damage": 45}, "strategy": "远程风筝，持续输出", "counters": ["高速刺客", "控制技能"], "win_rate": 75}, {"profession": "mage", "build_name": "控制法师", "core_equipment": ["玄机·星河法杖", "星辰·法袍", "元素·法师戒"], "key_attributes": {"control_power": 40, "element_penetration": 4000, "mp": 3000, "cooldown_reduction": 15}, "strategy": "控制先手，法术爆发", "counters": ["高抗性职业", "快速接近"], "win_rate": 72}], "tier_a_builds": [{"profession": "swordsman", "build_name": "平衡剑客", "core_equipment": ["百炼·龙吟剑", "龙鳞·霸者战甲", "龙心·霸者项链"], "key_attributes": {"attack": 2200, "hp": 15000, "crit_rate": 25, "defense": 1800}, "strategy": "攻守平衡，持久作战", "counters": ["高爆发职业", "风筝流"], "win_rate": 68}, {"profession": "monk", "build_name": "坦克武僧", "core_equipment": ["铁衣·山岳重锤", "铁山·不动甲", "守护·生命手镯"], "key_attributes": {"hp": 20000, "defense": 2500, "damage_reduction": 20, "control_resist": 60}, "strategy": "极限生存，反击制胜", "counters": ["持续输出", "百分比伤害"], "win_rate": 65}], "tier_b_builds": [{"profession": "doctor", "build_name": "生存医师", "core_equipment": ["素问·回春羽扇", "治愈·圣者长袍", "神明·祝福项链"], "key_attributes": {"heal_bonus": 50, "mp": 3500, "recovery": 45, "dodge_rate": 20}, "strategy": "持续治疗，消耗对手", "counters": ["高爆发", "治疗减效"], "win_rate": 58}]}, "attribute_priorities": {"high_priority": ["crit_rate", "crit_damage", "speed", "dodge_rate"], "medium_priority": ["attack", "hit_rate", "control_power", "control_resist"], "low_priority": ["hp", "defense", "recovery", "mp"]}}, {"id": "bangzhan", "name": "帮战", "mode_type": "团队战", "level_requirement": 70, "current_meta": {"team_compositions": [{"name": "标准配置", "roles": {"main_tank": "monk", "off_tank": "swordsman", "main_dps": "archer", "burst_dps": "assassin", "control": "mage", "support": "doctor"}, "strategy": "稳扎稳打，团队配合", "win_rate": 72}, {"name": "速攻流", "roles": {"tank": "swordsman", "dps1": "assassin", "dps2": "archer", "dps3": "mage", "support1": "doctor", "support2": "doctor"}, "strategy": "快速击杀关键目标", "win_rate": 68}], "key_strategies": {"focus_fire": "集火优先级：医师 > 法师 > 弓手 > 刺客 > 剑客 > 武僧", "positioning": "远程职业保持距离，近战职业保护后排", "timing": "控制技能配合，爆发时机把握"}}, "attribute_priorities": {"tank_roles": ["hp", "defense", "control_resist", "team_buff"], "dps_roles": ["attack", "crit_damage", "penetration", "speed"], "support_roles": ["heal_bonus", "mp", "recovery", "team_support"]}}, {"id": "lun<PERSON><PERSON>", "name": "论剑", "mode_type": "排位赛", "level_requirement": 80, "current_meta": {"season_trends": {"current_season": "第12赛季", "dominant_professions": ["assassin", "archer", "mage"], "emerging_builds": ["混合刺客（攻击+生存）", "控制弓手（控制+输出）", "坦克法师（生存+控制）"], "banned_equipment": [], "popular_combinations": [{"equipment_set": ["血魅·噬魂刃", "影舞·夜行衣", "破军·战意手镯"], "usage_rate": 35, "win_rate": 76}, {"equipment_set": ["流星·追月弓", "风影·疾行衣", "疾风·敏捷戒指"], "usage_rate": 28, "win_rate": 73}]}}, "ranking_factors": {"equipment_score_weight": 0.4, "skill_level_weight": 0.3, "strategy_weight": 0.2, "luck_weight": 0.1}}], "general_pvp_strategies": {"attribute_weights": {"pvp_general": {"crit_rate": 1.0, "crit_damage": 0.95, "speed": 0.9, "dodge_rate": 0.85, "attack": 0.8, "control_power": 0.75, "hit_rate": 0.7, "hp": 0.6, "defense": 0.5, "recovery": 0.3}, "profession_specific": {"assassin": {"crit_rate": 1.0, "crit_damage": 1.0, "speed": 0.95, "dodge_rate": 0.9}, "archer": {"attack": 1.0, "hit_rate": 0.95, "crit_damage": 0.9, "speed": 0.8}, "mage": {"control_power": 1.0, "element_penetration": 0.95, "mp": 0.8, "cooldown_reduction": 0.85}, "swordsman": {"attack": 0.9, "hp": 0.8, "crit_rate": 0.85, "defense": 0.7}, "monk": {"hp": 1.0, "defense": 0.95, "control_resist": 0.9, "damage_reduction": 0.85}, "doctor": {"heal_bonus": 1.0, "mp": 0.9, "recovery": 0.85, "dodge_rate": 0.7}}}, "counter_strategies": {"vs_assassin": {"recommended_attributes": ["hp", "defense", "dodge_rate", "control_power"], "equipment_focus": "生存装备，控制技能", "tactics": "保持距离，预判走位"}, "vs_archer": {"recommended_attributes": ["speed", "dodge_rate", "gap_closer", "control_power"], "equipment_focus": "机动装备，接近技能", "tactics": "快速接近，打断射击"}, "vs_mage": {"recommended_attributes": ["element_resist", "magic_shield", "speed", "silence"], "equipment_focus": "法术防护，沉默技能", "tactics": "抗性装备，打断施法"}, "vs_tank": {"recommended_attributes": ["penetration", "true_damage", "percentage_damage", "patience"], "equipment_focus": "穿透装备，持续输出", "tactics": "穿透防御，持久战"}}}}