#!/usr/bin/env python3
"""
测试DeepSeek-V3模型是否正常工作
"""
import asyncio
import httpx
import json

async def test_deepseek_v3():
    """测试DeepSeek-V3模型"""
    print("🧪 测试DeepSeek-V3模型...")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # 测试问题列表
        test_questions = [
            "你好，你现在使用的是什么模型？",
            "请分析一下逆水寒手游的装备系统",
            "作为AI，你如何看待游戏平衡性？",
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"📝 测试 {i}: {question}")
            print("-" * 40)
            
            try:
                response = await client.post(
                    "http://localhost:8000/api/v1/learning/chat",
                    json={"message": question}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data["success"]:
                        print("✅ DeepSeek-V3回复成功")
                        print(f"🎯 意图: {data['intent_analysis']['intent']['primary_intent']}")
                        print(f"🎲 置信度: {data['intent_analysis']['confidence']}")
                        print(f"🔧 行动: {data['intent_analysis']['action_taken']}")
                        
                        print("\n🤖 AI回复:")
                        response_lines = data["agent_response"].split('\n')
                        for line in response_lines[:10]:  # 显示前10行
                            if line.strip():
                                print(f"   {line}")
                        
                        if len(response_lines) > 10:
                            print("   ... (回复较长，已截断)")
                    else:
                        print("⚠️ 回复失败，使用降级模式")
                        
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
            
            print("\n" + "=" * 50 + "\n")
            await asyncio.sleep(1)  # 短暂延迟
        
        # 检查Agent状态
        print("📊 检查Agent状态...")
        try:
            response = await client.get("http://localhost:8000/api/v1/learning/agent/status")
            if response.status_code == 200:
                data = response.json()
                
                if data["agent_initialized"]:
                    status = data["status"]
                    print("✅ Agent状态:")
                    print(f"   对话次数: {status['conversation_count']}")
                    print(f"   知识条目: {status['knowledge_items']}")
                    print(f"   学习需求: {status['learning_needs']}")
                    print(f"   能力列表: {', '.join(status['capabilities'])}")
                else:
                    print("⚠️ Agent尚未初始化")
            else:
                print(f"❌ 获取状态失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 状态查询异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 DeepSeek-V3测试完成!")
    print("\n💡 模型特点:")
    print("✅ DeepSeek-V3 - 最新的推理模型")
    print("✅ 强大的中文理解能力")
    print("✅ 优秀的逻辑推理能力")
    print("✅ 专业的游戏分析能力")

async def main():
    await test_deepseek_v3()

if __name__ == "__main__":
    asyncio.run(main())
