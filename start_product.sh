#!/bin/bash

# 逆水寒智能装备助手 - 产品启动脚本

echo "🚀 启动逆水寒智能装备助手 (产品版本)..."

# 检查Python是否安装
if ! command -v python &> /dev/null; then
    echo "❌ Python未安装，请先安装Python 3.8+"
    exit 1
fi

# 检查是否在项目根目录
if [ ! -f "backend/app/main.py" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data
mkdir -p backend/static

# 检查并安装依赖
echo "📦 检查Python依赖..."
cd backend

# 安装基础依赖
echo "⬇️ 安装依赖包..."
pip install fastapi uvicorn httpx loguru pydantic pydantic-settings python-dotenv

# 启动后端服务
echo "🔧 启动后端服务..."
echo ""
echo "📱 应用地址: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/docs"
echo "🤖 学习Agent: http://localhost:8000/api/v1/learning/status"
echo ""
echo "💡 按 Ctrl+C 停止服务"
echo ""

# 启动应用
python app/main.py
