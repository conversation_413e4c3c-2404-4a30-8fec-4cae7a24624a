#!/usr/bin/env python3
"""
测试修复编码问题后的Agent
"""
import asyncio
import httpx
import json

async def test_fixed_agent():
    """测试修复后的Agent"""
    print("测试修复编码问题后的Agent...")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=20.0) as client:
        
        # 测试问题
        test_message = "你好，你是什么模型？请证明你是真正的AI。"
        
        print(f"发送消息: {test_message}")
        print("-" * 40)
        
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/learning/chat",
                json={"message": test_message}
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"成功: {data['success']}")
                
                if data["success"]:
                    print("Agent回复:")
                    print(data["agent_response"])
                    
                    # 检查是否是真正的AI回复
                    if "DeepSeek-V3" in data["agent_response"]:
                        print("\n*** 成功！这是真正的DeepSeek-V3回复！***")
                    else:
                        print("\n*** 警告：这可能还是预设回复 ***")
                        
                else:
                    print("使用了降级回复:")
                    print(data.get("fallback_response", "无降级回复"))
                    
            else:
                print(f"请求失败: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成")

async def main():
    await test_fixed_agent()

if __name__ == "__main__":
    asyncio.run(main())
