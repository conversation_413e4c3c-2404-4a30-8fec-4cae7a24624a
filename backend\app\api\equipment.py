"""
装备相关API路由
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
from loguru import logger

from ..models.equipment import (
    Equipment, EquipmentScore, EquipmentComparison, EquipmentRecommendation,
    ScenarioType, ProfessionType, EquipmentType
)
from ..services.deepseek_service import deepseek_service
from ..services.equipment_service import equipment_service

router = APIRouter(prefix="/equipment", tags=["装备"])


@router.post("/analyze", response_model=EquipmentScore)
async def analyze_equipment(
    equipment: Equipment,
    scenario: ScenarioType,
    profession: ProfessionType
):
    """
    分析装备并给出评分
    
    - **equipment**: 装备信息
    - **scenario**: 使用场景 (PVE/PVP)
    - **profession**: 职业类型
    """
    try:
        score = await deepseek_service.analyze_equipment(equipment, scenario, profession)
        
        if not score:
            raise HTTPException(status_code=500, detail="装备分析失败")
        
        return score
        
    except Exception as e:
        logger.error(f"装备分析异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/compare", response_model=EquipmentComparison)
async def compare_equipment(
    equipment_a: Equipment,
    equipment_b: Equipment,
    scenario: ScenarioType,
    profession: ProfessionType
):
    """
    对比两件装备
    
    - **equipment_a**: 装备A
    - **equipment_b**: 装备B
    - **scenario**: 使用场景
    - **profession**: 职业类型
    """
    try:
        # 分别分析两件装备
        score_a = await deepseek_service.analyze_equipment(equipment_a, scenario, profession)
        score_b = await deepseek_service.analyze_equipment(equipment_b, scenario, profession)
        
        if not score_a or not score_b:
            raise HTTPException(status_code=500, detail="装备对比分析失败")
        
        # 构建对比结果
        winner = equipment_a.id if score_a.total_score > score_b.total_score else equipment_b.id
        score_difference = abs(score_a.total_score - score_b.total_score)
        
        comparison_details = {
            f"{equipment_a.name}评分": f"{score_a.total_score:.1f}",
            f"{equipment_b.name}评分": f"{score_b.total_score:.1f}",
            "评分差异": f"{score_difference:.1f}",
            "推荐选择": equipment_a.name if winner == equipment_a.id else equipment_b.name
        }
        
        return EquipmentComparison(
            equipment_a=equipment_a,
            equipment_b=equipment_b,
            scenario=scenario,
            profession=profession,
            winner=winner,
            score_difference=score_difference,
            comparison_details=comparison_details
        )
        
    except Exception as e:
        logger.error(f"装备对比异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recommend", response_model=EquipmentRecommendation)
async def get_equipment_recommendations(
    profession: ProfessionType,
    scenario: ScenarioType,
    min_level: int = Query(1, ge=1, le=100, description="最低等级"),
    max_level: int = Query(100, ge=1, le=100, description="最高等级"),
    equipment_type: Optional[EquipmentType] = Query(None, description="装备类型筛选")
):
    """
    获取装备推荐
    
    - **profession**: 职业类型
    - **scenario**: 使用场景
    - **min_level**: 最低等级
    - **max_level**: 最高等级
    - **equipment_type**: 装备类型筛选（可选）
    """
    try:
        if min_level > max_level:
            raise HTTPException(status_code=400, detail="最低等级不能大于最高等级")
        
        # 获取符合条件的装备
        available_equipment = await equipment_service.get_equipment_by_criteria(
            level_range=(min_level, max_level),
            equipment_type=equipment_type
        )
        
        if not available_equipment:
            raise HTTPException(status_code=404, detail="未找到符合条件的装备")
        
        # 获取AI推荐建议
        build_suggestions = await deepseek_service.get_equipment_recommendations(
            profession, scenario, (min_level, max_level), available_equipment
        )
        
        # 根据场景和职业确定优先属性
        priority_attributes = equipment_service.get_priority_attributes(profession, scenario)
        
        # 筛选推荐装备（取评分最高的前10件）
        scored_equipment = []
        for equipment in available_equipment[:20]:  # 限制分析数量以控制API成本
            score = await deepseek_service.analyze_equipment(equipment, scenario, profession)
            if score:
                scored_equipment.append((equipment, score.total_score))
        
        # 按评分排序并取前10
        scored_equipment.sort(key=lambda x: x[1], reverse=True)
        recommended_equipment = [item[0] for item in scored_equipment[:10]]
        
        return EquipmentRecommendation(
            profession=profession,
            scenario=scenario,
            level_range=(min_level, max_level),
            recommended_equipment=recommended_equipment,
            build_suggestions=build_suggestions,
            priority_attributes=priority_attributes
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"装备推荐异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=List[Equipment])
async def list_equipment(
    equipment_type: Optional[EquipmentType] = Query(None, description="装备类型"),
    min_level: int = Query(1, ge=1, le=100, description="最低等级"),
    max_level: int = Query(100, ge=1, le=100, description="最高等级"),
    quality: Optional[str] = Query(None, description="装备品质"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制")
):
    """
    获取装备列表
    
    - **equipment_type**: 装备类型筛选
    - **min_level**: 最低等级
    - **max_level**: 最高等级
    - **quality**: 装备品质筛选
    - **limit**: 返回数量限制
    """
    try:
        equipment_list = await equipment_service.get_equipment_list(
            equipment_type=equipment_type,
            level_range=(min_level, max_level),
            quality=quality,
            limit=limit
        )
        
        return equipment_list
        
    except Exception as e:
        logger.error(f"获取装备列表异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{equipment_id}", response_model=Equipment)
async def get_equipment_detail(equipment_id: str):
    """
    获取装备详情
    
    - **equipment_id**: 装备ID
    """
    try:
        equipment = await equipment_service.get_equipment_by_id(equipment_id)
        
        if not equipment:
            raise HTTPException(status_code=404, detail="装备不存在")
        
        return equipment
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取装备详情异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))
