import React from 'react'
import { Card, Typography, Alert } from 'antd'
import { StarOutlined } from '@ant-design/icons'

const { Title } = Typography

const EquipmentRecommendation: React.FC = () => {
  return (
    <div className="fade-in">
      <Card title={
        <div>
          <StarOutlined /> 装备推荐
        </div>
      }>
        <Alert
          message="功能开发中"
          description="智能装备推荐功能正在开发中，敬请期待！"
          type="info"
          showIcon
        />
        
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <Title level={3} style={{ color: '#999' }}>
            🚧 功能开发中...
          </Title>
          <p style={{ color: '#666' }}>
            即将支持基于职业和场景的个性化装备推荐
          </p>
        </div>
      </Card>
    </div>
  )
}

export default EquipmentRecommendation
