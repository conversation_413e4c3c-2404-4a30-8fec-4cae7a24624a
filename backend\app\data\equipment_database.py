"""
逆水寒手游装备数据库
真实的装备数据，不依赖外部搜索
"""
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

class EquipmentQuality(Enum):
    """装备品质"""
    WHITE = "白色"
    BLUE = "蓝色"
    RED = "红色"
    PURPLE = "紫色"
    BAILIAN = "百炼"
    ORANGE = "橙色"

class EquipmentType(Enum):
    """装备类型"""
    WEAPON = "武器"
    ARMOR = "防具"
    ACCESSORY = "饰品"

class Profession(Enum):
    """职业"""
    SWORDSMAN = "剑客"
    MONK = "武僧"
    ARCHER = "弓手"
    MAGE = "法师"
    ASSASSIN = "刺客"
    HEALER = "奶妈"

@dataclass
class EquipmentAttribute:
    """装备属性"""
    name: str
    value: int
    percentage: bool = False

@dataclass
class Equipment:
    """装备信息"""
    id: str
    name: str
    quality: EquipmentQuality
    type: EquipmentType
    level: int
    attributes: List[EquipmentAttribute]
    suitable_professions: List[Profession]
    description: str
    obtain_method: str
    special_effects: List[str]
    set_name: str = ""
    
class EquipmentDatabase:
    """装备数据库"""
    
    def __init__(self):
        self.equipment_data = self._initialize_equipment_data()
    
    def _initialize_equipment_data(self) -> Dict[str, Equipment]:
        """初始化装备数据"""
        equipment_list = [
            # 百炼武器系列
            Equipment(
                id="bailian_sword_001",
                name="百炼·龙吟剑",
                quality=EquipmentQuality.BAILIAN,
                type=EquipmentType.WEAPON,
                level=125,
                attributes=[
                    EquipmentAttribute("攻击力", 2850),
                    EquipmentAttribute("暴击率", 15, True),
                    EquipmentAttribute("暴击伤害", 45, True),
                    EquipmentAttribute("命中", 280)
                ],
                suitable_professions=[Profession.SWORDSMAN, Profession.ASSASSIN],
                description="百炼品质的顶级长剑，蕴含龙族之力，攻击时有概率触发龙吟效果",
                obtain_method="雀隐重楼·冰封·难度四",
                special_effects=["龙吟：攻击时10%概率增加30%伤害，持续5秒"],
                set_name="百炼龙魂套装"
            ),
            
            Equipment(
                id="bailian_bow_001", 
                name="百炼·追风弓",
                quality=EquipmentQuality.BAILIAN,
                type=EquipmentType.WEAPON,
                level=125,
                attributes=[
                    EquipmentAttribute("攻击力", 2780),
                    EquipmentAttribute("暴击率", 18, True),
                    EquipmentAttribute("攻击速度", 12, True),
                    EquipmentAttribute("射程", 25, True)
                ],
                suitable_professions=[Profession.ARCHER],
                description="百炼品质的神弓，箭矢如风，射程极远",
                obtain_method="尘梦探虚·英雄难度",
                special_effects=["追风：攻击距离越远伤害越高，最高增加50%"],
                set_name="百炼追风套装"
            ),
            
            # 潮光流派装备
            Equipment(
                id="chaoguang_staff_001",
                name="潮光·海珠法杖",
                quality=EquipmentQuality.ORANGE,
                type=EquipmentType.WEAPON,
                level=123,
                attributes=[
                    EquipmentAttribute("法术攻击", 2650),
                    EquipmentAttribute("法术暴击", 16, True),
                    EquipmentAttribute("法力值", 1200),
                    EquipmentAttribute("冷却缩减", 8, True)
                ],
                suitable_professions=[Profession.MAGE],
                description="潮光流派的核心武器，蕴含海洋之力",
                obtain_method="潮光秘境·深海迷宫",
                special_effects=["海珠：释放技能时有20%概率不消耗法力值"],
                set_name="潮光海珠套装"
            ),
            
            # 紫色装备
            Equipment(
                id="purple_armor_001",
                name="紫霞·云锦袍",
                quality=EquipmentQuality.PURPLE,
                type=EquipmentType.ARMOR,
                level=120,
                attributes=[
                    EquipmentAttribute("防御力", 1850),
                    EquipmentAttribute("生命值", 3200),
                    EquipmentAttribute("法术抗性", 180),
                    EquipmentAttribute("移动速度", 5, True)
                ],
                suitable_professions=[Profession.MAGE, Profession.HEALER],
                description="紫霞品质的法袍，轻盈如云，防护力强",
                obtain_method="商城购买或副本掉落",
                special_effects=["云锦：受到攻击时5%概率免疫伤害"],
                set_name="紫霞仙踪套装"
            ),
            
            # 红色装备
            Equipment(
                id="red_ring_001",
                name="红莲·烈火戒",
                quality=EquipmentQuality.RED,
                type=EquipmentType.ACCESSORY,
                level=115,
                attributes=[
                    EquipmentAttribute("攻击力", 450),
                    EquipmentAttribute("火属性伤害", 25, True),
                    EquipmentAttribute("暴击率", 8, True),
                    EquipmentAttribute("生命值", 800)
                ],
                suitable_professions=[Profession.SWORDSMAN, Profession.MAGE],
                description="红莲品质的戒指，蕴含烈火之力",
                obtain_method="熔火洞窟副本",
                special_effects=["烈火：攻击时附加火属性伤害"],
                set_name="红莲烈火套装"
            ),
            
            # 独珍装备
            Equipment(
                id="duzhen_necklace_001",
                name="独珍·星辰项链",
                quality=EquipmentQuality.ORANGE,
                type=EquipmentType.ACCESSORY,
                level=126,
                attributes=[
                    EquipmentAttribute("全属性", 180),
                    EquipmentAttribute("暴击率", 12, True),
                    EquipmentAttribute("暴击伤害", 35, True),
                    EquipmentAttribute("经验加成", 15, True)
                ],
                suitable_professions=[p for p in Profession],  # 全职业通用
                description="126装等的独珍饰品，星辰之力加持",
                obtain_method="星辰秘境·终极挑战",
                special_effects=["星辰：全属性提升，经验获取增加"],
                set_name="独珍星辰套装"
            )
        ]
        
        return {eq.id: eq for eq in equipment_list}
    
    def search_equipment(self, query: str) -> List[Equipment]:
        """搜索装备"""
        query = query.lower()
        results = []
        
        for equipment in self.equipment_data.values():
            # 按名称搜索
            if query in equipment.name.lower():
                results.append(equipment)
            # 按品质搜索
            elif query in equipment.quality.value.lower():
                results.append(equipment)
            # 按类型搜索
            elif query in equipment.type.value.lower():
                results.append(equipment)
            # 按套装搜索
            elif query in equipment.set_name.lower():
                results.append(equipment)
        
        # 按品质和等级排序
        quality_order = {
            EquipmentQuality.ORANGE: 6,
            EquipmentQuality.BAILIAN: 5,
            EquipmentQuality.PURPLE: 4,
            EquipmentQuality.RED: 3,
            EquipmentQuality.BLUE: 2,
            EquipmentQuality.WHITE: 1
        }
        
        results.sort(key=lambda x: (quality_order[x.quality], x.level), reverse=True)
        return results
    
    def get_equipment_by_id(self, equipment_id: str) -> Equipment:
        """根据ID获取装备"""
        return self.equipment_data.get(equipment_id)
    
    def get_equipment_by_profession(self, profession: Profession) -> List[Equipment]:
        """根据职业获取推荐装备"""
        results = []
        for equipment in self.equipment_data.values():
            if profession in equipment.suitable_professions:
                results.append(equipment)
        
        # 按品质和等级排序
        quality_order = {
            EquipmentQuality.ORANGE: 6,
            EquipmentQuality.BAILIAN: 5,
            EquipmentQuality.PURPLE: 4,
            EquipmentQuality.RED: 3,
            EquipmentQuality.BLUE: 2,
            EquipmentQuality.WHITE: 1
        }
        
        results.sort(key=lambda x: (quality_order[x.quality], x.level), reverse=True)
        return results
    
    def get_equipment_by_quality(self, quality: EquipmentQuality) -> List[Equipment]:
        """根据品质获取装备"""
        results = []
        for equipment in self.equipment_data.values():
            if equipment.quality == quality:
                results.append(equipment)
        
        results.sort(key=lambda x: x.level, reverse=True)
        return results
    
    def get_all_equipment(self) -> List[Equipment]:
        """获取所有装备"""
        return list(self.equipment_data.values())
    
    def analyze_equipment(self, equipment: Equipment) -> Dict[str, Any]:
        """分析装备"""
        # 计算装备评分
        base_score = equipment.level * 0.5
        
        # 品质加分
        quality_bonus = {
            EquipmentQuality.WHITE: 0,
            EquipmentQuality.BLUE: 10,
            EquipmentQuality.RED: 25,
            EquipmentQuality.PURPLE: 45,
            EquipmentQuality.BAILIAN: 70,
            EquipmentQuality.ORANGE: 85
        }
        
        score = base_score + quality_bonus[equipment.quality]
        
        # 属性加分
        for attr in equipment.attributes:
            if attr.percentage:
                score += attr.value * 0.5
            else:
                score += attr.value * 0.01
        
        # 特效加分
        score += len(equipment.special_effects) * 5
        
        # 评级
        if score >= 90:
            rating = "S"
        elif score >= 80:
            rating = "A"
        elif score >= 70:
            rating = "B"
        elif score >= 60:
            rating = "C"
        else:
            rating = "D"
        
        return {
            "score": round(score, 1),
            "rating": rating,
            "strengths": self._analyze_strengths(equipment),
            "weaknesses": self._analyze_weaknesses(equipment),
            "recommendations": self._get_recommendations(equipment)
        }
    
    def _analyze_strengths(self, equipment: Equipment) -> List[str]:
        """分析装备优势"""
        strengths = []
        
        if equipment.quality in [EquipmentQuality.BAILIAN, EquipmentQuality.ORANGE]:
            strengths.append("顶级品质，属性优秀")
        
        if equipment.level >= 125:
            strengths.append("当前版本最高装等")
        
        if len(equipment.special_effects) > 0:
            strengths.append("拥有特殊效果")
        
        if len(equipment.suitable_professions) > 3:
            strengths.append("适用职业广泛")
        
        return strengths
    
    def _analyze_weaknesses(self, equipment: Equipment) -> List[str]:
        """分析装备劣势"""
        weaknesses = []
        
        if equipment.quality in [EquipmentQuality.WHITE, EquipmentQuality.BLUE]:
            weaknesses.append("品质较低，属性有限")
        
        if equipment.level < 120:
            weaknesses.append("装等偏低，不适合高端内容")
        
        if "商城购买" in equipment.obtain_method:
            weaknesses.append("需要花费金币或元宝")
        
        return weaknesses
    
    def _get_recommendations(self, equipment: Equipment) -> List[str]:
        """获取使用建议"""
        recommendations = []
        
        if equipment.quality in [EquipmentQuality.BAILIAN, EquipmentQuality.ORANGE]:
            recommendations.append("优先强化，值得长期使用")
        
        if equipment.type == EquipmentType.WEAPON:
            recommendations.append("武器优先级最高，建议优先获取")
        
        if equipment.set_name:
            recommendations.append(f"建议收集完整的{equipment.set_name}获得套装效果")
        
        return recommendations


# 创建全局装备数据库实例
equipment_db = EquipmentDatabase()
