#!/usr/bin/env python3
"""
社交媒体知识库服务
从小红书、抖音等平台获取最新的逆水寒手游攻略和心得
"""
import asyncio
import httpx
import json
import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from loguru import logger

class SocialKnowledgeService:
    """社交媒体知识库服务"""
    
    def __init__(self):
        self.platforms = {
            "xiaohongshu": {
                "name": "小红书",
                "keywords": [
                    "逆水寒手游", "逆水寒手游攻略", "逆水寒手游装备", "逆水寒手游职业", "逆水寒手游碎梦",
                    "逆水寒手游PVP", "逆水寒手游副本", "逆水寒手游新手", "逆水寒手游技能", "逆水寒手游神相",
                    "逆水寒手游最新", "逆水寒手游2025", "逆水寒手游更新", "逆水寒手游meta"
                ],
                "api_available": True,  # 使用真实爬虫
                "priority": 1
            },
            "douyin": {
                "name": "抖音",
                "keywords": [
                    "逆水寒手游", "逆水寒手游攻略", "逆水寒手游PVP", "逆水寒手游副本", "逆水寒手游碎梦",
                    "逆水寒手游装备", "逆水寒手游职业", "逆水寒手游技巧", "逆水寒手游教学", "逆水寒手游神相",
                    "逆水寒手游最新", "逆水寒手游2025", "逆水寒手游更新", "逆水寒手游实战"
                ],
                "api_available": True,  # 使用真实爬虫
                "priority": 2
            },
            "bilibili": {
                "name": "B站",
                "keywords": [
                    "逆水寒手游", "逆水寒手游攻略", "逆水寒手游测评", "逆水寒手游解说", "逆水寒手游碎梦",
                    "逆水寒手游教程", "逆水寒手游实况", "逆水寒手游分析", "逆水寒手游最新", "逆水寒手游神相",
                    "逆水寒手游2025", "逆水寒手游更新", "逆水寒手游meta", "逆水寒手游竞技"
                ],
                "api_available": True,  # B站有公开API
                "priority": 3
            }
        }
        self.knowledge_cache = {}
        self.last_update = {}
        # 使用绝对路径确保文件保存在正确位置
        import os
        from pathlib import Path
        project_root = Path(__file__).parent.parent.parent
        self.cache_file = project_root / "data" / "social_knowledge_cache.json"
        
    async def initialize(self):
        """初始化知识库服务"""
        logger.info("初始化社交媒体知识库服务...")

        # 加载缓存数据
        await self._load_cache()

        # 初始化各平台连接
        for platform_id, platform_info in self.platforms.items():
            try:
                if platform_info["api_available"]:
                    await self._test_platform_connection(platform_id)
                    logger.info(f"✅ {platform_info['name']} 连接成功")
                else:
                    logger.info(f"⚠️ {platform_info['name']} 需要配置爬虫接口")
            except Exception as e:
                logger.error(f"❌ {platform_info['name']} 连接失败: {e}")
    
    async def _test_platform_connection(self, platform_id: str) -> bool:
        """测试平台连接"""
        if platform_id == "bilibili":
            # 测试B站API
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.get(
                        "https://api.bilibili.com/x/web-interface/search/all/v2",
                        params={"keyword": "逆水寒手游", "page": 1}
                    )
                    return response.status_code == 200
            except Exception:
                return False
        elif platform_id in ["xiaohongshu", "douyin"]:
            # 测试爬虫服务
            try:
                # 导入爬虫服务
                try:
                    from .real_crawler_service import real_crawler_service
                    logger.info(f"爬虫服务已加载，准备测试{platform_id}连接")
                    return True
                except ImportError:
                    logger.warning(f"爬虫服务未找到，{platform_id}连接测试失败")
                    return False
            except Exception as e:
                logger.error(f"测试{platform_id}连接失败: {e}")
                return False
        return False
    
    async def update_knowledge_from_social_media(self) -> Dict[str, Any]:
        """从社交媒体更新知识库"""
        logger.info("🔄 开始从社交媒体更新知识库...")
        
        update_results = {
            "success": True,
            "platforms_updated": [],
            "new_knowledge_count": 0,
            "errors": [],
            "timestamp": datetime.now().isoformat()
        }
        
        for platform_id, platform_info in self.platforms.items():
            try:
                logger.info(f"📱 更新 {platform_info['name']} 知识...")
                
                if platform_info["api_available"]:
                    knowledge_items = await self._fetch_from_platform(platform_id)
                else:
                    # 模拟数据，实际需要配置爬虫
                    knowledge_items = await self._simulate_platform_data(platform_id)
                
                if knowledge_items:
                    processed_count = await self._process_knowledge_items(
                        platform_id, knowledge_items
                    )
                    
                    update_results["platforms_updated"].append({
                        "platform": platform_info["name"],
                        "items_count": processed_count
                    })
                    update_results["new_knowledge_count"] += processed_count

                    # 立即保存缓存，防止数据丢失
                    await self._save_cache()

                    logger.info(f"✅ {platform_info['name']} 更新完成: {processed_count}条")
                
            except Exception as e:
                error_msg = f"{platform_info['name']} 更新失败: {str(e)}"
                logger.error(f"❌ {error_msg}")
                update_results["errors"].append(error_msg)
        
        # 更新缓存时间
        self.last_update[datetime.now().strftime("%Y-%m-%d")] = update_results

        # 保存缓存到文件
        await self._save_cache()

        logger.info(f"🎉 知识库更新完成: 新增{update_results['new_knowledge_count']}条知识")
        return update_results
    
    async def _fetch_from_platform(self, platform_id: str) -> List[Dict]:
        """从平台获取数据"""
        if platform_id == "bilibili":
            return await self._fetch_from_bilibili()
        elif platform_id in ["xiaohongshu", "douyin"]:
            return await self._fetch_from_social_media(platform_id)
        return []

    async def _fetch_from_social_media(self, platform_id: str) -> List[Dict]:
        """从社交媒体获取真实数据"""
        try:
            from .real_crawler_service import real_crawler_service

            keywords = self.platforms[platform_id]["keywords"]
            logger.info(f"开始爬取{self.platforms[platform_id]['name']}最新数据...")

            if platform_id == "xiaohongshu":
                results = await real_crawler_service.crawl_xiaohongshu(keywords)
            elif platform_id == "douyin":
                results = await real_crawler_service.crawl_douyin(keywords)
            else:
                results = []

            logger.info(f"{self.platforms[platform_id]['name']}爬取完成，获得{len(results)}条数据")
            return results

        except Exception as e:
            logger.error(f"爬取{platform_id}失败: {e}")
            # 如果爬取失败，使用模拟数据作为备用
            logger.info(f"使用{platform_id}备用数据")
            return await self._simulate_platform_data(platform_id)
    
    async def _fetch_from_bilibili(self) -> List[Dict]:
        """从B站获取逆水寒相关视频"""
        try:
            async with httpx.AsyncClient(timeout=15.0) as client:
                # 搜索逆水寒手游相关视频
                response = await client.get(
                    "https://api.bilibili.com/x/web-interface/search/all/v2",
                    params={
                        "keyword": "逆水寒手游 攻略",
                        "page": 1,
                        "page_size": 20,
                        "order": "pubdate"  # 按发布时间排序
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == 0:
                        videos = data.get("data", {}).get("result", {}).get("video", [])
                        
                        knowledge_items = []
                        for video in videos[:10]:  # 取前10个
                            knowledge_items.append({
                                "title": video.get("title", ""),
                                "description": video.get("description", ""),
                                "author": video.get("author", ""),
                                "play_count": video.get("play", 0),
                                "duration": video.get("duration", ""),
                                "url": f"https://www.bilibili.com/video/{video.get('bvid', '')}",
                                "publish_time": video.get("pubdate", 0),
                                "tags": video.get("tag", "").split(",") if video.get("tag") else []
                            })
                        
                        return knowledge_items
                        
        except Exception as e:
            logger.error(f"B站数据获取失败: {e}")
        
        return []
    
    async def _simulate_platform_data(self, platform_id: str) -> List[Dict]:
        """模拟平台数据（实际需要配置爬虫）"""
        platform_info = self.platforms[platform_id]
        
        # 模拟不同平台的热门内容
        if platform_id == "xiaohongshu":
            return [
                {
                    "title": "逆水寒手游新手必看！职业选择全攻略",
                    "content": "碎梦适合喜欢高爆发的玩家，神相适合团队辅助...",
                    "author": "游戏达人小红",
                    "likes": 1250,
                    "comments": 89,
                    "tags": ["逆水寒手游", "新手攻略", "职业选择"],
                    "platform": "小红书",
                    "url": "模拟链接",
                    "publish_time": datetime.now() - timedelta(hours=2)
                },
                {
                    "title": "逆水寒装备强化技巧，少走弯路！",
                    "content": "装备强化有技巧，这些方法能提高成功率...",
                    "author": "攻略小能手",
                    "likes": 890,
                    "comments": 67,
                    "tags": ["逆水寒手游", "装备强化", "技巧"],
                    "platform": "小红书",
                    "url": "模拟链接",
                    "publish_time": datetime.now() - timedelta(hours=5)
                }
            ]
        
        elif platform_id == "douyin":
            return [
                {
                    "title": "逆水寒PVP实战技巧分享",
                    "content": "PVP中的连招技巧和走位要点...",
                    "author": "游戏高手",
                    "likes": 2340,
                    "comments": 156,
                    "shares": 78,
                    "tags": ["逆水寒手游", "PVP", "实战技巧"],
                    "platform": "抖音",
                    "url": "模拟链接",
                    "publish_time": datetime.now() - timedelta(hours=1)
                }
            ]
        
        return []
    
    async def _process_knowledge_items(self, platform_id: str, items: List[Dict]) -> int:
        """处理知识条目"""
        processed_count = 0
        
        for item in items:
            try:
                # 提取关键信息
                knowledge_item = {
                    "id": f"{platform_id}_{hash(item.get('title', ''))}",
                    "platform": self.platforms[platform_id]["name"],
                    "title": item.get("title", ""),
                    "content": item.get("content", item.get("description", "")),
                    "author": item.get("author", ""),
                    "engagement": {
                        "likes": item.get("likes", item.get("play_count", 0)),
                        "comments": item.get("comments", 0),
                        "shares": item.get("shares", 0)
                    },
                    "tags": item.get("tags", []),
                    "url": item.get("url", ""),
                    "publish_time": item.get("publish_time", datetime.now()),
                    "extracted_time": datetime.now(),
                    "relevance_score": self._calculate_relevance_score(item)
                }
                
                # 存储到缓存
                cache_key = f"{platform_id}_knowledge"
                if cache_key not in self.knowledge_cache:
                    self.knowledge_cache[cache_key] = []
                
                self.knowledge_cache[cache_key].append(knowledge_item)
                processed_count += 1
                
            except Exception as e:
                logger.error(f"处理知识条目失败: {e}")
        
        return processed_count
    
    def _calculate_relevance_score(self, item: Dict) -> float:
        """计算内容相关性得分 - 优先最新内容"""
        score = 0.0

        # 标题相关性 (30%)
        title = item.get("title", "").lower()
        if "逆水寒手游" in title:
            score += 0.2
        elif "逆水寒" in title and "手游" not in title:
            # 如果只有"逆水寒"但没有"手游"，降低分数（可能是PC端内容）
            score += 0.05
        if any(keyword in title for keyword in ["攻略", "技巧", "心得", "最新", "新版本"]):
            score += 0.1

        # 发布时间权重最高 (50%) - 最新优先！
        publish_time = item.get("publish_time")
        if isinstance(publish_time, datetime):
            hours_ago = (datetime.now() - publish_time).total_seconds() / 3600
            if hours_ago <= 6:  # 6小时内
                score += 0.5
            elif hours_ago <= 24:  # 24小时内
                score += 0.4
            elif hours_ago <= 72:  # 3天内
                score += 0.3
            elif hours_ago <= 168:  # 1周内
                score += 0.2
            elif hours_ago <= 720:  # 1个月内
                score += 0.1

        # 互动数据权重降低 (20%) - 作为参考
        likes = item.get("likes", item.get("play_count", 0))
        if likes > 5000:
            score += 0.2
        elif likes > 1000:
            score += 0.15
        elif likes > 500:
            score += 0.1
        elif likes > 100:
            score += 0.05

        return min(score, 1.0)
    
    async def get_trending_knowledge(self, category: str = "all") -> List[Dict]:
        """获取热门知识 - 优先最新内容"""
        all_knowledge = []

        for cache_key, items in self.knowledge_cache.items():
            all_knowledge.extend(items)

        # 按时间优先排序：先按相关性得分，再按发布时间
        sorted_knowledge = sorted(
            all_knowledge,
            key=lambda x: (
                x["relevance_score"],  # 主要排序：相关性得分（包含时间权重）
                x.get("publish_time", datetime.min)  # 次要排序：发布时间
            ),
            reverse=True
        )

        return sorted_knowledge[:20]  # 返回前20条
    
    async def search_knowledge(self, query: str) -> List[Dict]:
        """搜索知识库 - 增强匹配"""
        results = []
        query_lower = query.lower()

        # 扩展关键词匹配
        search_keywords = [query_lower]
        if "碎梦" in query_lower:
            search_keywords.extend(["碎梦", "pvp", "对战", "竞技"])
        if "pvp" in query_lower:
            search_keywords.extend(["pvp", "对战", "竞技", "pk"])
        if "装备" in query_lower:
            search_keywords.extend(["装备", "搭配", "配置", "推荐"])

        logger.info(f"搜索关键词: {search_keywords}")

        for cache_key, items in self.knowledge_cache.items():
            for item in items:
                match_score = 0

                # 多关键词匹配计分
                for keyword in search_keywords:
                    if keyword in item["title"].lower():
                        match_score += 3
                    if keyword in item["content"].lower():
                        match_score += 2
                    if any(keyword in tag.lower() for tag in item["tags"]):
                        match_score += 1

                if match_score > 0:
                    item_copy = item.copy()
                    item_copy["match_score"] = match_score
                    results.append(item_copy)

        logger.info(f"搜索'{query}'找到{len(results)}条结果")

        # 按匹配分数和相关性排序
        return sorted(results, key=lambda x: (x["match_score"], x["relevance_score"]), reverse=True)
    
    def get_knowledge_stats(self) -> Dict[str, Any]:
        """获取知识库统计"""
        total_items = sum(len(items) for items in self.knowledge_cache.values())
        
        platform_stats = {}
        for cache_key, items in self.knowledge_cache.items():
            platform = cache_key.replace("_knowledge", "")
            platform_stats[platform] = len(items)
        
        return {
            "total_knowledge_items": total_items,
            "platform_breakdown": platform_stats,
            "last_update": max(self.last_update.keys()) if self.last_update else "从未更新",
            "cache_status": "活跃" if total_items > 0 else "空"
        }

    async def _save_cache(self):
        """保存缓存到文件"""
        try:
            # 确保目录存在
            os.makedirs(self.cache_file.parent, exist_ok=True)

            # 统计要保存的数据
            total_items = sum(len(items) for items in self.knowledge_cache.values())
            logger.info(f"准备保存 {total_items} 条知识到缓存文件")

            cache_data = {
                "knowledge_cache": self.knowledge_cache,
                "last_update": self.last_update,
                "saved_at": datetime.now().isoformat(),
                "total_items": total_items
            }

            # 序列化datetime对象
            def serialize_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2, default=serialize_datetime)

            logger.info(f"✅ 缓存已保存到 {self.cache_file}，包含 {total_items} 条数据")
        except Exception as e:
            logger.error(f"❌ 保存缓存失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    async def _load_cache(self):
        """从文件加载缓存"""
        try:
            logger.info(f"尝试从 {self.cache_file} 加载缓存")

            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                self.knowledge_cache = cache_data.get("knowledge_cache", {})
                self.last_update = cache_data.get("last_update", {})

                # 恢复datetime对象
                for cache_key, items in self.knowledge_cache.items():
                    for item in items:
                        if "publish_time" in item and isinstance(item["publish_time"], str):
                            try:
                                item["publish_time"] = datetime.fromisoformat(item["publish_time"])
                            except:
                                item["publish_time"] = datetime.now()
                        if "extracted_time" in item and isinstance(item["extracted_time"], str):
                            try:
                                item["extracted_time"] = datetime.fromisoformat(item["extracted_time"])
                            except:
                                item["extracted_time"] = datetime.now()

                total_items = sum(len(items) for items in self.knowledge_cache.values())
                saved_at = cache_data.get("saved_at", "未知")
                logger.info(f"✅ 从缓存加载了 {total_items} 条知识 (保存时间: {saved_at})")
            else:
                logger.info(f"❌ 缓存文件不存在: {self.cache_file}")
                self.knowledge_cache = {}
                self.last_update = {}
        except Exception as e:
            logger.error(f"❌ 加载缓存失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            self.knowledge_cache = {}
            self.last_update = {}


# 创建全局实例
social_knowledge_service = SocialKnowledgeService()
