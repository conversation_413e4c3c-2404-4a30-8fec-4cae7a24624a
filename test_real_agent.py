#!/usr/bin/env python3
"""
测试真正的AI Agent功能
"""
import asyncio
import httpx
import json

async def test_real_agent():
    """测试真正的Agent"""
    print("🤖 测试真正的AI Agent功能...")
    print("=" * 60)
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # 测试问题列表
        test_questions = [
            "百炼武器有哪些？",
            "推荐一个PVP装备搭配",
            "你能做什么？",
            "逆水寒手游当前版本是什么？",
            "我是新手，应该怎么开始？"
        ]
        
        print("🎯 开始与Agent对话测试...\n")
        
        for i, question in enumerate(test_questions, 1):
            print(f"📝 测试 {i}: {question}")
            print("-" * 40)
            
            try:
                # 发送消息给Agent
                response = await client.post(
                    "http://localhost:8000/api/v1/learning/chat",
                    json={
                        "message": question,
                        "context": None
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data["success"]:
                        print("✅ Agent回复成功")
                        print(f"🎯 意图分析: {data['intent_analysis']['intent']['primary_intent']}")
                        print(f"🎲 置信度: {data['intent_analysis']['confidence']:.2f}")
                        print(f"🔧 采取行动: {data['intent_analysis']['action_taken']}")
                        print(f"🧠 学习状态: {'已学习' if data['learned_something'] else '未学习'}")
                        print("\n💬 Agent回复:")
                        
                        # 显示回复的前几行
                        response_lines = data["agent_response"].split('\n')
                        for line in response_lines[:8]:  # 只显示前8行
                            if line.strip():
                                print(f"   {line}")
                        
                        if len(response_lines) > 8:
                            print("   ... (回复较长，已截断)")
                            
                    else:
                        print("⚠️ Agent回复失败，使用降级回复")
                        print(f"错误: {data.get('error', '未知错误')}")
                        
                        # 显示降级回复的前几行
                        fallback_lines = data["fallback_response"].split('\n')
                        for line in fallback_lines[:5]:
                            if line.strip():
                                print(f"   {line}")
                
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
            
            print("\n" + "=" * 60 + "\n")
            
            # 短暂延迟
            await asyncio.sleep(1)
        
        # 获取Agent状态
        print("📊 获取Agent状态...")
        try:
            response = await client.get("http://localhost:8000/api/v1/learning/agent/status")
            if response.status_code == 200:
                data = response.json()
                
                if data["agent_initialized"]:
                    status = data["status"]
                    print("✅ Agent状态:")
                    print(f"   对话次数: {status['conversation_count']}")
                    print(f"   知识条目: {status['knowledge_items']}")
                    print(f"   学习需求: {status['learning_needs']}")
                    print(f"   最后交互: {status['last_interaction']}")
                    print(f"   能力列表: {', '.join(status['capabilities'])}")
                else:
                    print("⚠️ Agent尚未初始化")
            else:
                print(f"❌ 获取状态失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 状态查询异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Agent测试完成!")
    print("\n💡 真正的Agent特点:")
    print("✅ 意图理解 - 分析用户真实需求")
    print("✅ 诚实回应 - 承认知识限制，不编造信息")
    print("✅ 学习能力 - 记录学习需求，持续改进")
    print("✅ 智能决策 - 根据情况选择合适的回应策略")
    print("✅ 状态跟踪 - 记录对话历史和学习进度")
    print("\n🔗 打开聊天界面: http://localhost:8000/frontend/agent_chat.html")

async def main():
    await test_real_agent()

if __name__ == "__main__":
    asyncio.run(main())
