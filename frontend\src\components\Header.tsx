import React from 'react'
import { Layout, Typography, Space, Button } from 'antd'
import { GithubOutlined, ApiOutlined } from '@ant-design/icons'

const { Header: AntHeader } = Layout
const { Title } = Typography

const Header: React.FC = () => {
  return (
    <AntHeader style={{ 
      background: '#001529', 
      padding: '0 24px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Title level={3} style={{ color: 'white', margin: 0 }}>
          ⚔️ 逆水寒智能装备助手
        </Title>
        <span style={{ color: '#1890ff', marginLeft: 16, fontSize: '12px' }}>
          Powered by DeepSeek AI
        </span>
      </div>
      
      <Space>
        <Button 
          type="text" 
          icon={<ApiOutlined />}
          style={{ color: 'white' }}
          onClick={() => window.open('/api/v1/docs', '_blank')}
        >
          API文档
        </Button>
        <Button 
          type="text" 
          icon={<GithubOutlined />}
          style={{ color: 'white' }}
          onClick={() => window.open('https://github.com/your-repo/nsh-agent', '_blank')}
        >
          GitHub
        </Button>
      </Space>
    </AntHeader>
  )
}

export default Header
