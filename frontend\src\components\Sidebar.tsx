import React from 'react'
import { Layout, Menu } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  HomeOutlined,
  BarChartOutlined,
  SwapOutlined,
  StarOutlined,
  UnorderedListOutlined,
  RobotOutlined
} from '@ant-design/icons'

const { Sider } = Layout

const Sidebar: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/analysis',
      icon: <BarChartOutlined />,
      label: '装备分析',
    },
    {
      key: '/comparison',
      icon: <SwapOutlined />,
      label: '装备对比',
    },
    {
      key: '/recommendation',
      icon: <StarOutlined />,
      label: '装备推荐',
    },
    {
      key: '/equipment',
      icon: <UnorderedListOutlined />,
      label: '装备库',
    },
    {
      key: '/learning',
      icon: <RobotOutlined />,
      label: '学习Agent',
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  return (
    <Sider width={200} style={{ background: '#fff' }}>
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        style={{ height: '100%', borderRight: 0 }}
        items={menuItems}
        onClick={handleMenuClick}
      />
    </Sider>
  )
}

export default Sidebar
