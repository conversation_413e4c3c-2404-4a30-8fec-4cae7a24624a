#!/usr/bin/env python3
"""
测试真实数据集成效果
"""
import asyncio
import httpx

async def test_real_data_integration():
    """测试真实数据集成"""
    print("测试真实数据集成效果")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=90.0) as client:
        
        # 1. 检查知识库状态
        print("1. 检查知识库状态")
        try:
            response = await client.get("http://localhost:8000/api/v1/social-knowledge/stats")
            if response.status_code == 200:
                data = response.json()
                stats = data["stats"]
                print(f"   总知识条目: {stats['total_knowledge_items']}")
                print(f"   平台分布: {stats['platform_breakdown']}")
                print(f"   最后更新: {stats['last_update']}")
            else:
                print(f"   获取统计失败: {response.status_code}")
        except Exception as e:
            print(f"   异常: {e}")
        
        # 2. 测试Agent利用真实数据
        print("\n2. 测试Agent利用真实数据回复")
        test_questions = [
            "逆水寒PVP有什么最新攻略？",
            "逆水寒副本怎么打？",
            "逆水寒手游有什么新手建议？"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n   测试 {i}: {question}")
            print("   " + "-" * 40)
            
            try:
                response = await client.post(
                    "http://localhost:8000/api/v1/learning/chat",
                    json={"message": question}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        reply = data["agent_response"]
                        print("   成功!")
                        if len(reply) > 200:
                            print(f"   回复: {reply[:200]}...")
                        else:
                            print(f"   回复: {reply}")
                        
                        # 检查是否引用了社交媒体数据
                        if "抖音" in reply or "小红书" in reply or "来源" in reply:
                            print("   ✅ 检测到社交媒体数据集成!")
                        else:
                            print("   ⚠️ 未明显检测到社交媒体数据")
                    else:
                        print(f"   失败: {data.get('error', '未知错误')}")
                else:
                    print(f"   HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"   异常: {e}")
            
            await asyncio.sleep(3)
        
        # 3. 搜索特定内容
        print("\n3. 搜索特定内容")
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/social-knowledge/search",
                json={"query": "PVP", "limit": 3}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   搜索'PVP'找到 {data['total_found']} 条结果")
                
                for i, item in enumerate(data["search_results"], 1):
                    print(f"   {i}. {item['title']}")
                    print(f"      平台: {item['platform']}, 热度: {item['engagement']['likes']}赞")
            else:
                print(f"   搜索失败: {response.status_code}")
                
        except Exception as e:
            print(f"   搜索异常: {e}")
    
    print("\n" + "=" * 50)
    print("真实数据集成测试完成!")

async def main():
    await test_real_data_integration()

if __name__ == "__main__":
    asyncio.run(main())
