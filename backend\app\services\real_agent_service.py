"""
真正的AI Agent服务
具备学习、推理、决策能力的智能Agent
"""
import asyncio
import httpx
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger

from app.services.ai_service import AIService
from app.core.config import get_settings

settings = get_settings()


class RealAgentService:
    """真正的AI Agent"""
    
    def __init__(self, ai_service: AIService):
        self.ai_service = ai_service
        self.conversation_history = []
        self.knowledge_base = {
            "learned_facts": [],
            "user_preferences": {},
            "game_insights": [],
            "uncertainty_areas": []
        }

        # 引入社交知识库服务
        try:
            from app.services.social_knowledge_service import social_knowledge_service
            self.social_knowledge = social_knowledge_service
            logger.info("社交知识库服务已集成到Agent")
        except ImportError:
            self.social_knowledge = None
            logger.warning("社交知识库服务不可用")
        
    async def chat_with_user(self, user_message: str, context: Dict = None) -> Dict[str, Any]:
        """与用户对话 - 真正的Agent交互"""
        logger.info(f"Agent收到用户消息: {user_message}")
        
        # 1. 理解用户意图
        intent = await self._analyze_user_intent(user_message)
        
        # 2. 检查知识库
        relevant_knowledge = self._search_knowledge_base(user_message)
        
        # 3. 决定行动策略
        action_plan = await self._decide_action(user_message, intent, relevant_knowledge)
        
        # 4. 执行行动
        response = await self._execute_action(action_plan, user_message, context)
        
        # 5. 学习和更新
        await self._learn_from_interaction(user_message, response, intent)
        
        return {
            "response": response,
            "intent": intent,
            "action_taken": action_plan["action"],
            "confidence": action_plan["confidence"],
            "learned_something": action_plan.get("learned", False),
            "timestamp": datetime.now().isoformat()
        }
    
    async def _analyze_user_intent(self, message: str) -> Dict[str, Any]:
        """分析用户意图"""
        # 使用AI分析用户真正想要什么
        analysis_prompt = f"""
分析用户消息的意图："{message}"

请判断用户想要：
1. 查询装备信息
2. 寻求游戏建议
3. 了解游戏机制
4. 比较装备
5. 其他需求

同时判断：
- 用户的游戏水平（新手/进阶/高手）
- 关注的重点（PVP/PVE/收集）
- 紧急程度（立即需要/一般咨询）

以JSON格式返回分析结果。
"""
        
        try:
            if self.ai_service.is_available():
                ai_response = await self.ai_service._make_request(analysis_prompt, "intent_analysis")
                if ai_response:
                    # 尝试解析AI的回复
                    return self._parse_intent_response(ai_response)
        except Exception as e:
            logger.error(f"意图分析失败: {e}")
        
        # 降级处理：简单关键词匹配
        return self._simple_intent_analysis(message)
    
    def _simple_intent_analysis(self, message: str) -> Dict[str, Any]:
        """简单的意图分析"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["装备", "武器", "防具", "饰品"]):
            return {
                "primary_intent": "equipment_query",
                "confidence": 0.8,
                "user_level": "unknown",
                "focus": "equipment"
            }
        elif any(word in message_lower for word in ["推荐", "建议", "怎么", "如何"]):
            return {
                "primary_intent": "seek_advice",
                "confidence": 0.7,
                "user_level": "beginner",
                "focus": "guidance"
            }
        elif any(word in message_lower for word in ["比较", "对比", "哪个好"]):
            return {
                "primary_intent": "comparison",
                "confidence": 0.8,
                "user_level": "intermediate",
                "focus": "decision_making"
            }
        else:
            return {
                "primary_intent": "general_inquiry",
                "confidence": 0.5,
                "user_level": "unknown",
                "focus": "information"
            }
    
    def _search_knowledge_base(self, query: str) -> List[Dict]:
        """搜索知识库中的相关信息"""
        relevant_info = []
        
        # 搜索已学习的事实
        for fact in self.knowledge_base["learned_facts"]:
            if any(word in fact["content"].lower() for word in query.lower().split()):
                relevant_info.append(fact)
        
        # 搜索游戏洞察
        for insight in self.knowledge_base["game_insights"]:
            if any(word in insight["topic"].lower() for word in query.lower().split()):
                relevant_info.append(insight)
        
        return relevant_info
    
    async def _decide_action(self, message: str, intent: Dict, knowledge: List) -> Dict[str, Any]:
        """决定采取什么行动"""
        action_plan = {
            "action": "respond",
            "confidence": 0.5,
            "reasoning": "",
            "needs_search": False,
            "needs_learning": False
        }
        
        # 根据意图决定行动
        if intent["primary_intent"] == "equipment_query":
            if not knowledge:
                action_plan.update({
                    "action": "search_and_learn",
                    "needs_search": True,
                    "needs_learning": True,
                    "reasoning": "用户询问装备信息，但我的知识库中没有相关信息，需要搜索学习"
                })
            else:
                action_plan.update({
                    "action": "provide_known_info",
                    "confidence": 0.8,
                    "reasoning": "知识库中有相关信息，可以直接回答"
                })
        
        elif intent["primary_intent"] == "seek_advice":
            action_plan.update({
                "action": "provide_advice",
                "needs_search": True,
                "reasoning": "用户寻求建议，需要结合最新信息给出专业建议"
            })
        
        elif intent["primary_intent"] == "comparison":
            action_plan.update({
                "action": "compare_and_analyze",
                "needs_search": True,
                "reasoning": "用户要求比较，需要获取准确信息进行对比分析"
            })
        
        return action_plan
    
    async def _execute_action(self, action_plan: Dict, message: str, context: Dict = None) -> str:
        """执行决定的行动"""
        action = action_plan["action"]
        
        if action == "search_and_learn":
            return await self._search_and_learn_response(message)
        
        elif action == "provide_known_info":
            return await self._provide_known_info_response(message)
        
        elif action == "provide_advice":
            return await self._provide_advice_response(message)
        
        elif action == "compare_and_analyze":
            return await self._compare_and_analyze_response(message)
        
        else:
            return await self._general_response(message)
    
    async def _search_and_learn_response(self, message: str) -> str:
        """搜索学习并回应 - 使用真正的AI"""
        logger.info(f"搜索学习回应: {message}")

        # 构建诚实的AI提示词
        ai_prompt = f"""
用户询问：{message}

作为诚实的逆水寒手游AI助手，你必须：

🚫 绝对不能：
- 编造任何具体的游戏数据、版本号、时间
- 假装知道最新的游戏状态
- 给出未经验证的具体信息

✅ 必须做到：
- 明确承认知识限制
- 引导用户查看官方信息
- 只提供通用建议和方法

回复格式：
"我无法提供准确的[具体信息]，因为游戏数据会持续更新。建议您通过以下方式获取最新信息：
1. 游戏内公告和帮助
2. 官方网站和社交媒体
3. 游戏社区和论坛

我可以分享一些通用的[相关建议]..."

请诚实回复：
"""

        try:
            # 强制重新测试AI连接
            logger.info("强制重新测试AI连接...")
            connection_test = await self.ai_service._test_connection()
            logger.info(f"连接测试结果: {connection_test}")

            if connection_test:
                # 强制设置为可用
                self.ai_service.available = True
                logger.info("强制设置AI服务为可用")

                ai_response = await self.ai_service._make_request(ai_prompt, "general")
                if ai_response:
                    # 记录学习需求
                    await self._record_learning_need(message)
                    return f"**DeepSeek-V3 回复**\n\n{ai_response}"
                else:
                    logger.error("AI回复为空！")
                    return "AI回复为空，请检查AI服务"
            else:
                logger.error("AI连接测试失败！")
                return "AI连接测试失败"
        except Exception as e:
            logger.error(f"AI回复失败: {e}")
            return f"AI回复异常: {str(e)}"
    
    async def _provide_known_info_response(self, message: str) -> str:
        """提供已知信息"""
        relevant_knowledge = self._search_knowledge_base(message)
        
        if relevant_knowledge:
            response = "🤖 **基于我学到的信息**：\n\n"
            for info in relevant_knowledge:
                response += f"• {info['content']}\n"
                response += f"  (来源：{info['source']}，学习时间：{info['learned_at']})\n\n"
            
            response += "**注意**：以上信息基于我之前的学习，可能需要验证最新性。"
        else:
            response = await self._search_and_learn_response(message)
        
        return response
    
    async def _provide_advice_response(self, message: str) -> str:
        """提供建议 - 使用真正的AI"""
        logger.info(f"提供建议回应: {message}")

        # 尝试从社交知识库获取相关内容
        social_context = ""
        if self.social_knowledge:
            try:
                social_results = await self.social_knowledge.search_knowledge(message)
                if social_results:
                    social_context = "\n\n参考社交媒体热门内容：\n"
                    for item in social_results[:2]:  # 取前2条
                        social_context += f"- {item['title']} (来源：{item['platform']}，{item['engagement']['likes']}赞)\n"
                        social_context += f"  {item['content'][:100]}...\n"
            except Exception as e:
                logger.warning(f"获取社交知识库内容失败: {e}")

        # 构建建议提示词
        ai_prompt = f"""
用户寻求建议：{message}

作为诚实的逆水寒手游AI助手，请提供有用的建议。要求：

🚫 绝对禁止：
- 编造具体的装备名称、属性数值
- 编造版本信息、更新时间
- 给出未经验证的具体配装方案

✅ 必须做到：
- 提供通用的游戏机制建议
- 说明获取准确信息的途径
- 承认信息的局限性
- 给出实用的方法论

{social_context}

请提供诚实且有用的建议：
"""

        try:
            # 强制重新测试AI连接
            logger.info("强制重新测试AI连接...")
            connection_test = await self.ai_service._test_connection()
            logger.info(f"连接测试结果: {connection_test}")

            if connection_test:
                # 强制设置为可用
                self.ai_service.available = True
                logger.info("强制设置AI服务为可用")

                ai_response = await self.ai_service._make_request(ai_prompt, "general")
                if ai_response:
                    logger.info(f"AI建议生成成功: {ai_response[:100]}...")
                    return f"**DeepSeek-V3 建议**\n\n{ai_response}"
                else:
                    logger.error("AI建议回复为空！")
                    return "AI建议回复为空，请检查AI服务"
            else:
                logger.error("AI连接测试失败！")
                return "AI连接测试失败"
        except Exception as e:
            logger.error(f"AI建议失败: {e}")
            return f"AI建议异常: {str(e)}"
    
    async def _compare_and_analyze_response(self, message: str) -> str:
        """比较分析回应 - 使用真正的AI"""
        logger.info(f"比较分析回应: {message}")

        # 尝试从社交知识库获取相关内容
        social_context = ""
        if self.social_knowledge:
            try:
                social_results = await self.social_knowledge.search_knowledge(message)
                if social_results:
                    social_context = "\n\n参考社交媒体热门内容：\n"
                    for item in social_results[:2]:  # 取前2条
                        social_context += f"- {item['title']} (来源：{item['platform']}，{item['engagement']['likes']}赞)\n"
                        social_context += f"  {item['content'][:100]}...\n"
            except Exception as e:
                logger.warning(f"获取社交知识库内容失败: {e}")

        # 构建比较分析提示词
        ai_prompt = f"""
用户要求比较分析：{message}

作为诚实的逆水寒手游AI助手，请提供比较分析方法。要求：

🚫 绝对禁止：
- 编造具体的装备属性对比
- 编造伤害数值、属性数据
- 给出未经验证的优劣结论

✅ 必须做到：
- 提供比较分析的方法论
- 说明获取准确数据的途径
- 承认数据的局限性
- 给出通用的分析框架

{social_context}

请提供诚实的分析方法：
"""

        try:
            # 强制重新测试AI连接
            logger.info("强制重新测试AI连接...")
            connection_test = await self.ai_service._test_connection()
            logger.info(f"连接测试结果: {connection_test}")

            if connection_test:
                # 强制设置为可用
                self.ai_service.available = True
                logger.info("强制设置AI服务为可用")

                ai_response = await self.ai_service._make_request(ai_prompt, "general")
                if ai_response:
                    logger.info(f"AI分析生成成功: {ai_response[:100]}...")
                    return f"**DeepSeek-V3 分析**\n\n{ai_response}"
                else:
                    logger.error("AI分析回复为空！")
                    return "AI分析回复为空，请检查AI服务"
            else:
                logger.error("AI连接测试失败！")
                return "AI连接测试失败"
        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            return f"AI分析异常: {str(e)}"
    
    async def _general_response(self, message: str) -> str:
        """通用回应 - 使用真正的AI"""
        logger.info(f"AI Agent: 使用真正的AI生成回复: {message}")

        # 尝试从社交知识库获取相关内容
        social_context = ""
        if self.social_knowledge:
            try:
                # 扩展搜索关键词
                search_terms = [message]
                if "碎梦" in message:
                    search_terms.extend(["碎梦", "PVP", "职业", "技能"])
                if "PVP" in message:
                    search_terms.extend(["PVP", "竞技", "对战"])
                if "装备" in message:
                    search_terms.extend(["装备", "搭配", "推荐"])

                social_results = []
                for term in search_terms:
                    results = await self.social_knowledge.search_knowledge(term)
                    social_results.extend(results)

                # 去重并排序
                unique_results = {item['id']: item for item in social_results}.values()
                sorted_results = sorted(unique_results, key=lambda x: x['relevance_score'], reverse=True)

                if sorted_results:
                    social_context = "\n\n🔥 最新社交媒体热门内容：\n"
                    for item in sorted_results[:3]:  # 取前3条
                        social_context += f"- {item['title']} (来源：{item['platform']}，{item['engagement']['likes']}赞)\n"
                        social_context += f"  {item['content'][:150]}...\n\n"

                    logger.info(f"找到{len(sorted_results)}条相关社交媒体内容")
                else:
                    # 如果没找到，尝试获取热门内容
                    trending_results = await self.social_knowledge.get_trending_knowledge()
                    if trending_results:
                        social_context = "\n\n🔥 当前热门游戏内容（可能相关）：\n"
                        for item in trending_results[:2]:
                            social_context += f"- {item['title']} (来源：{item['platform']}，{item['engagement']['likes']}赞)\n"
                            social_context += f"  {item['content'][:100]}...\n\n"
                        logger.info(f"使用热门内容作为备选，共{len(trending_results)}条")
                    else:
                        logger.info("未找到任何社交媒体内容")
            except Exception as e:
                logger.warning(f"获取社交知识库内容失败: {e}")

        # 构建智能AI提示词
        if social_context:
            # 有社交媒体数据时，使用数据回答
            ai_prompt = f"""
用户问题：{message}

你是逆水寒手游AI助手，我为你提供了最新的社交媒体热门内容作为参考：

{social_context}

请基于以上最新信息回答用户问题，要求：
✅ 优先使用提供的社交媒体内容
✅ 可以引用具体的热门内容和玩家经验
✅ 说明信息来源（小红书、抖音、B站等）
✅ 提供实用的建议和分析
✅ 保持专业和友好的语调

如果社交媒体内容不够完整，可以补充通用建议，但要明确区分哪些是基于最新数据，哪些是通用建议。
"""
        else:
            # 没有社交媒体数据时，诚实回答
            ai_prompt = f"""
用户问题：{message}

你是逆水寒手游AI助手，但目前没有找到相关的最新社交媒体内容。请诚实回答：

🚫 不要编造具体数据
✅ 承认信息限制
✅ 引导用户查看官方渠道
✅ 提供通用建议

回复格式：
"关于{message}，我目前没有找到最新的社交媒体讨论内容。建议您：
1. 查看游戏内最新公告
2. 关注官方社交媒体
3. 查看游戏社区热门讨论

我可以提供一些通用的相关建议..."
"""

        try:
            # 强制重新测试AI连接（增加超时时间）
            logger.info("强制重新测试AI连接...")
            connection_test = await self.ai_service._test_connection()
            logger.info(f"连接测试结果: {connection_test}")

            if connection_test:
                # 强制设置为可用
                self.ai_service.available = True
                logger.info("强制设置AI服务为可用")

                ai_response = await self.ai_service._make_request(ai_prompt, "general")
                if ai_response:
                    logger.info(f"AI生成回复成功: {ai_response[:100]}...")
                    return f"**DeepSeek-V3 回复**\n\n{ai_response}"
                else:
                    logger.error("AI回复为空！")
                    return "AI回复为空，请检查AI服务"
            else:
                logger.error("AI连接测试失败！")
                return "AI连接测试失败"
        except Exception as e:
            logger.error(f"AI回复失败: {e}")
            return f"AI回复异常: {str(e)}"
    
    async def _learn_from_interaction(self, user_message: str, response: str, intent: Dict):
        """从交互中学习"""
        # 记录对话历史
        self.conversation_history.append({
            "user_message": user_message,
            "agent_response": response,
            "intent": intent,
            "timestamp": datetime.now().isoformat()
        })
        
        # 识别学习机会
        if intent["primary_intent"] == "equipment_query":
            await self._record_learning_need(user_message)
    
    async def _record_learning_need(self, query: str):
        """记录学习需求"""
        learning_need = {
            "query": query,
            "type": "equipment_info",
            "priority": "high",
            "recorded_at": datetime.now().isoformat(),
            "status": "pending"
        }
        
        if "learning_needs" not in self.knowledge_base:
            self.knowledge_base["learning_needs"] = []
        
        self.knowledge_base["learning_needs"].append(learning_need)
    
    def _parse_intent_response(self, ai_response: str) -> Dict[str, Any]:
        """解析AI的意图分析回复"""
        try:
            # 尝试提取JSON
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                parsed = json.loads(json_str)

                # 智能字段映射 - 支持多种可能的字段名
                intent_fields = ["intention", "intent", "primary_intent", "main_intent"]
                level_fields = ["game_experience", "game_level", "user_level", "experience"]
                focus_fields = ["focus", "focus_area", "category", "type"]

                primary_intent = "general_inquiry"
                for field in intent_fields:
                    if field in parsed:
                        primary_intent = str(parsed[field])
                        break

                user_level = "unknown"
                for field in level_fields:
                    if field in parsed:
                        user_level = str(parsed[field])
                        break

                focus = "information"
                for field in focus_fields:
                    if field in parsed:
                        focus = str(parsed[field])
                        break

                # 标准化意图类型
                if "建议" in primary_intent or "推荐" in primary_intent:
                    primary_intent = "seek_advice"
                elif "PVP" in focus or "pvp" in focus:
                    primary_intent = "pvp_inquiry"
                elif "装备" in focus or "equipment" in focus:
                    primary_intent = "equipment_inquiry"
                elif "职业" in focus or "class" in focus:
                    primary_intent = "class_inquiry"

                return {
                    "primary_intent": primary_intent,
                    "confidence": 0.9,  # AI成功解析，给高置信度
                    "user_level": user_level,
                    "focus": focus
                }
        except Exception as e:
            logger.error(f"解析意图回复失败: {e}")

        # 降级到简单分析
        return {
            "primary_intent": "general_inquiry",
            "confidence": 0.3,
            "user_level": "unknown",
            "focus": "information"
        }
    
    def get_agent_status(self) -> Dict[str, Any]:
        """获取Agent状态"""
        return {
            "conversation_count": len(self.conversation_history),
            "knowledge_items": len(self.knowledge_base.get("learned_facts", [])),
            "learning_needs": len(self.knowledge_base.get("learning_needs", [])),
            "last_interaction": self.conversation_history[-1]["timestamp"] if self.conversation_history else None,
            "capabilities": [
                "意图理解",
                "知识搜索", 
                "学习记录",
                "诚实回应",
                "持续改进"
            ]
        }
