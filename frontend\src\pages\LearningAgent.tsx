import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Button, 
  Input, 
  Space, 
  Typography, 
  Alert,
  Spin,
  Progress,
  Tag,
  Divider,
  Row,
  Col,
  Timeline,
  message
} from 'antd'
import { 
  RobotOutlined, 
  SearchOutlined, 
  ReloadOutlined,
  BrainOutlined,
  GlobalOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'

const { Title, Paragraph, Text } = Typography
const { Search } = Input

interface AgentStatus {
  agent_status: string
  last_update: string | null
  knowledge_base_size: number
  update_count: number
  should_update: boolean
}

interface SearchResult {
  title: string
  url: string
  source: string
  date: string
  type: string
}

const LearningAgent: React.FC = () => {
  const [agentStatus, setAgentStatus] = useState<AgentStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [searchLoading, setSearchLoading] = useState(false)
  const [updateLoading, setUpdateLoading] = useState(false)

  // 获取Agent状态
  const fetchAgentStatus = async () => {
    try {
      const response = await fetch('/api/v1/learning/status')
      const data = await response.json()
      setAgentStatus(data)
    } catch (error) {
      console.error('获取Agent状态失败:', error)
    }
  }

  // 搜索游戏信息
  const handleSearch = async (query: string) => {
    if (!query.trim()) return
    
    setSearchLoading(true)
    try {
      const response = await fetch(`/api/v1/learning/search?query=${encodeURIComponent(query)}&max_results=10`)
      const data = await response.json()
      setSearchResults(data.results || [])
      message.success(`找到 ${data.count} 条相关信息`)
    } catch (error) {
      console.error('搜索失败:', error)
      message.error('搜索失败')
    } finally {
      setSearchLoading(false)
    }
  }

  // 触发知识库更新
  const handleUpdate = async () => {
    setUpdateLoading(true)
    try {
      const response = await fetch('/api/v1/learning/update', { method: 'POST' })
      const data = await response.json()
      message.success(data.message)
      
      // 延迟刷新状态
      setTimeout(() => {
        fetchAgentStatus()
      }, 2000)
    } catch (error) {
      console.error('更新失败:', error)
      message.error('更新失败')
    } finally {
      setUpdateLoading(false)
    }
  }

  // 实时分析装备
  const handleAnalyzeEquipment = async (equipmentName: string) => {
    if (!equipmentName.trim()) return
    
    setLoading(true)
    try {
      const response = await fetch(`/api/v1/learning/equipment/${encodeURIComponent(equipmentName)}`)
      const data = await response.json()
      
      // 显示分析结果
      message.success('装备分析完成，请查看控制台')
      console.log('装备分析结果:', data)
    } catch (error) {
      console.error('装备分析失败:', error)
      message.error('装备分析失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAgentStatus()
    
    // 定期刷新状态
    const interval = setInterval(fetchAgentStatus, 30000) // 30秒刷新一次
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'updating': return 'processing'
      case 'error': return 'error'
      default: return 'default'
    }
  }

  const getSourceColor = (source: string) => {
    switch (source) {
      case '官方网站': return 'red'
      case 'NGA论坛': return 'blue'
      case '哔哩哔哩': return 'cyan'
      default: return 'default'
    }
  }

  return (
    <div className="fade-in">
      <Card title={
        <Space>
          <RobotOutlined />
          智能学习Agent
        </Space>
      }>
        <Alert
          message="AI学习Agent"
          description="具备自主学习能力的智能Agent，能够实时从网络获取最新的逆水寒游戏信息，持续更新知识库"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* Agent状态 */}
        <Card title="Agent状态" size="small" style={{ marginBottom: 24 }}>
          {agentStatus ? (
            <Row gutter={16}>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <Tag color={getStatusColor(agentStatus.agent_status)} style={{ marginBottom: 8 }}>
                    {agentStatus.agent_status.toUpperCase()}
                  </Tag>
                  <div>运行状态</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <Text strong style={{ fontSize: 18, color: '#1890ff' }}>
                    {agentStatus.knowledge_base_size}
                  </Text>
                  <div>知识库大小</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <Text strong style={{ fontSize: 18, color: '#52c41a' }}>
                    {agentStatus.update_count}
                  </Text>
                  <div>更新次数</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <Tag color={agentStatus.should_update ? 'orange' : 'green'}>
                    {agentStatus.should_update ? '需要更新' : '最新状态'}
                  </Tag>
                  <div>更新状态</div>
                </div>
              </Col>
            </Row>
          ) : (
            <Spin />
          )}
          
          <Divider />
          
          <Space>
            <Button 
              type="primary" 
              icon={<ReloadOutlined />}
              loading={updateLoading}
              onClick={handleUpdate}
            >
              手动更新知识库
            </Button>
            <Button 
              icon={<ClockCircleOutlined />}
              onClick={fetchAgentStatus}
            >
              刷新状态
            </Button>
          </Space>
        </Card>

        {/* 实时搜索 */}
        <Card title="实时信息搜索" size="small" style={{ marginBottom: 24 }}>
          <Search
            placeholder="搜索逆水寒游戏信息（如：装备推荐、版本更新、meta构建等）"
            allowClear
            enterButton={<SearchOutlined />}
            size="large"
            loading={searchLoading}
            onSearch={handleSearch}
            style={{ marginBottom: 16 }}
          />
          
          {searchResults.length > 0 && (
            <div>
              <Title level={5}>搜索结果 ({searchResults.length}条)</Title>
              <Timeline>
                {searchResults.map((result, index) => (
                  <Timeline.Item key={index}>
                    <div>
                      <Space>
                        <Tag color={getSourceColor(result.source)}>{result.source}</Tag>
                        <Text type="secondary">{result.date}</Text>
                      </Space>
                      <div style={{ marginTop: 4 }}>
                        <a href={result.url} target="_blank" rel="noopener noreferrer">
                          {result.title}
                        </a>
                      </div>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </div>
          )}
        </Card>

        {/* 装备实时分析 */}
        <Card title="装备实时分析" size="small" style={{ marginBottom: 24 }}>
          <Paragraph>
            输入装备名称，Agent将从网络搜索最新信息并进行AI分析
          </Paragraph>
          
          <Search
            placeholder="输入装备名称（如：百炼武器、橙色护甲等）"
            allowClear
            enterButton={<BrainOutlined />}
            size="large"
            loading={loading}
            onSearch={handleAnalyzeEquipment}
          />
        </Card>

        {/* 功能特性 */}
        <Card title="Agent能力" size="small">
          <Row gutter={16}>
            <Col span={8}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <GlobalOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                <Title level={5}>网络搜索</Title>
                <Paragraph style={{ fontSize: 12 }}>
                  实时搜索官网、论坛、视频等多个渠道的最新信息
                </Paragraph>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <BrainOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                <Title level={5}>AI分析</Title>
                <Paragraph style={{ fontSize: 12 }}>
                  使用DeepSeek模型分析和理解搜索到的游戏信息
                </Paragraph>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <ReloadOutlined style={{ fontSize: 24, color: '#faad14', marginBottom: 8 }} />
                <Title level={5}>自动更新</Title>
                <Paragraph style={{ fontSize: 12 }}>
                  定期自动更新知识库，保持信息的时效性
                </Paragraph>
              </Card>
            </Col>
          </Row>
        </Card>
      </Card>
    </div>
  )
}

export default LearningAgent
