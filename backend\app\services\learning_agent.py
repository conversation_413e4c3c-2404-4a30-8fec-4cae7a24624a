"""
自主学习Agent - 持续从网络更新逆水寒游戏信息
"""
import asyncio
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import httpx
from bs4 import BeautifulSoup

from app.core.config import get_settings
from app.services.deepseek_service import deepseek_service

settings = get_settings()


@dataclass
class GameInfo:
    """游戏信息数据类"""
    version: str
    update_date: str
    equipment_system: Dict[str, Any]
    meta_info: Dict[str, Any]
    source_url: str
    confidence: float  # 信息可信度 0-1


class LearningAgent:
    """自主学习Agent"""
    
    def __init__(self):
        self.knowledge_base = {}
        self.last_update = None
        self.update_interval = timedelta(hours=2)  # 2小时更新一次，更频繁
        self.sources = [
            "https://h.163.com/news/",  # 官方新闻
            "https://bbs.nga.cn/thread.php?fid=650",  # NGA论坛
            "https://tieba.baidu.com/f?kw=逆水寒手游",  # 百度贴吧
        ]
    
    async def should_update(self) -> bool:
        """判断是否需要更新知识库"""
        if not self.last_update:
            return True
        return datetime.now() - self.last_update > self.update_interval
    
    async def search_latest_info(self, query: str, max_results: int = 10) -> List[Dict]:
        """搜索最新游戏信息"""
        search_results = []
        
        # 使用web-search工具搜索
        try:
            # 这里需要调用实际的搜索API
            # 暂时用模拟数据
            search_results = [
                {
                    "title": "逆水寒手游2.2.4版本更新",
                    "url": "https://h.163.com/news/update/20250116/37232_1207308.html",
                    "snippet": "新增潮光流派，125-126装等百炼装备",
                    "date": "2025-01-16"
                }
            ]
        except Exception as e:
            print(f"搜索失败: {e}")
        
        return search_results
    
    async def extract_equipment_info(self, content: str) -> Dict[str, Any]:
        """从内容中提取装备信息"""
        
        # 使用AI分析内容
        analysis_prompt = f"""
请分析以下逆水寒手游内容，提取装备系统相关信息：

内容：
{content[:2000]}  # 限制长度

请以JSON格式返回：
{{
    "equipment_qualities": ["白色", "蓝色", "红色", "紫色", "百炼", "橙色"],
    "equipment_levels": {{"min": 1, "max": 126}},
    "equipment_types": ["武器", "防具", "饰品"],
    "current_meta": {{"popular_builds": [], "tier_list": []}},
    "new_features": [],
    "version_info": {{"version": "", "date": ""}},
    "confidence": 0.8
}}
"""
        
        try:
            response = await deepseek_service._make_request([
                {"role": "system", "content": "你是逆水寒游戏数据分析专家"},
                {"role": "user", "content": analysis_prompt}
            ])
            
            if response:
                # 尝试解析JSON
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                if start_idx != -1 and end_idx > start_idx:
                    json_str = response[start_idx:end_idx]
                    return json.loads(json_str)
        except Exception as e:
            print(f"AI分析失败: {e}")
        
        return {}
    
    async def fetch_official_updates(self) -> List[GameInfo]:
        """获取官方更新信息"""
        game_infos = []
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 获取官方更新页面
                response = await client.get("https://h.163.com/news/")
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 查找更新公告链接
                    update_links = soup.find_all('a', href=re.compile(r'/news/update/'))
                    
                    for link in update_links[:3]:  # 只处理最新3个
                        update_url = "https://h.163.com" + link.get('href')
                        
                        # 获取具体更新内容
                        update_response = await client.get(update_url)
                        if update_response.status_code == 200:
                            update_content = update_response.text
                            
                            # AI分析更新内容
                            equipment_info = await self.extract_equipment_info(update_content)
                            
                            if equipment_info:
                                game_info = GameInfo(
                                    version=equipment_info.get('version_info', {}).get('version', 'unknown'),
                                    update_date=equipment_info.get('version_info', {}).get('date', ''),
                                    equipment_system=equipment_info,
                                    meta_info={},
                                    source_url=update_url,
                                    confidence=equipment_info.get('confidence', 0.5)
                                )
                                game_infos.append(game_info)
        
        except Exception as e:
            print(f"获取官方更新失败: {e}")
        
        return game_infos
    
    async def analyze_community_meta(self) -> Dict[str, Any]:
        """分析社区meta信息"""
        meta_info = {
            "popular_builds": [],
            "tier_rankings": {},
            "equipment_recommendations": {},
            "last_analyzed": datetime.now().isoformat()
        }
        
        # 搜索社区讨论
        search_queries = [
            "逆水寒手游 装备推荐 2025",
            "逆水寒手游 meta 构建",
            "逆水寒手游 PVP 最强",
            "逆水寒手游 PVE 攻略"
        ]
        
        for query in search_queries:
            try:
                results = await self.search_latest_info(query, 5)
                
                for result in results:
                    # 这里可以进一步分析每个搜索结果
                    # 提取meta信息
                    pass
                    
            except Exception as e:
                print(f"分析社区meta失败: {e}")
        
        return meta_info
    
    async def update_knowledge_base(self) -> bool:
        """更新知识库"""
        print("🔄 开始更新知识库...")
        
        try:
            # 1. 获取官方更新
            official_infos = await self.fetch_official_updates()
            print(f"📰 获取到 {len(official_infos)} 个官方更新")
            
            # 2. 分析社区meta
            meta_info = await self.analyze_community_meta()
            print("📊 完成社区meta分析")
            
            # 3. 更新知识库
            self.knowledge_base.update({
                "official_updates": [info.__dict__ for info in official_infos],
                "community_meta": meta_info,
                "last_update": datetime.now().isoformat(),
                "update_count": self.knowledge_base.get("update_count", 0) + 1
            })
            
            # 4. 保存到文件
            await self.save_knowledge_base()
            
            self.last_update = datetime.now()
            print("✅ 知识库更新完成")
            return True
            
        except Exception as e:
            print(f"❌ 知识库更新失败: {e}")
            return False
    
    async def save_knowledge_base(self):
        """保存知识库到文件"""
        try:
            with open("data/knowledge_base.json", "w", encoding="utf-8") as f:
                json.dump(self.knowledge_base, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存知识库失败: {e}")
    
    async def load_knowledge_base(self):
        """从文件加载知识库"""
        try:
            with open("data/knowledge_base.json", "r", encoding="utf-8") as f:
                self.knowledge_base = json.load(f)
                
            # 解析最后更新时间
            if "last_update" in self.knowledge_base:
                self.last_update = datetime.fromisoformat(self.knowledge_base["last_update"])
                
        except FileNotFoundError:
            print("知识库文件不存在，将创建新的知识库")
        except Exception as e:
            print(f"加载知识库失败: {e}")
    
    async def get_current_equipment_system(self) -> Dict[str, Any]:
        """获取当前装备系统信息"""
        if await self.should_update():
            await self.update_knowledge_base()
        
        # 从知识库中提取最新的装备系统信息
        equipment_system = {
            "qualities": ["白色", "蓝色", "红色", "紫色", "百炼", "橙色"],
            "max_level": 126,
            "types": ["武器", "防具", "饰品"],
            "current_version": "2.2.4",
            "last_updated": self.last_update.isoformat() if self.last_update else None
        }
        
        # 从知识库中获取更详细信息
        if "official_updates" in self.knowledge_base:
            for update in self.knowledge_base["official_updates"]:
                if update.get("confidence", 0) > 0.7:
                    equipment_system.update(update.get("equipment_system", {}))
        
        return equipment_system
    
    async def get_meta_recommendations(self, profession: str, scenario: str) -> Dict[str, Any]:
        """获取meta推荐"""
        if await self.should_update():
            await self.update_knowledge_base()
        
        # 从知识库中获取推荐
        recommendations = {
            "builds": [],
            "equipment_priority": [],
            "counter_strategies": [],
            "confidence": 0.5,
            "last_updated": self.last_update.isoformat() if self.last_update else None
        }
        
        # 从社区meta中获取推荐
        if "community_meta" in self.knowledge_base:
            meta = self.knowledge_base["community_meta"]
            # 这里可以根据profession和scenario筛选推荐
            recommendations.update(meta.get("equipment_recommendations", {}))
        
        return recommendations
    
    async def analyze_equipment_with_latest_info(self, equipment_data: Dict) -> Dict[str, Any]:
        """使用最新信息分析装备"""
        # 确保知识库是最新的
        current_system = await self.get_current_equipment_system()
        
        # 构建分析提示词，包含最新的游戏信息
        analysis_prompt = f"""
基于最新的逆水寒手游信息分析以下装备：

当前游戏版本: {current_system.get('current_version', 'unknown')}
装备品质体系: {current_system.get('qualities', [])}
最高装等: {current_system.get('max_level', 126)}

装备信息:
{json.dumps(equipment_data, ensure_ascii=False, indent=2)}

请提供专业的分析，包括：
1. 在当前版本中的评级
2. 适合的职业和场景
3. 与当前meta的匹配度
4. 改进建议

以JSON格式返回分析结果。
"""
        
        try:
            response = await deepseek_service._make_request([
                {"role": "system", "content": "你是逆水寒游戏装备分析专家，掌握最新的游戏信息和meta"},
                {"role": "user", "content": analysis_prompt}
            ])
            
            if response:
                # 解析AI回复
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                if start_idx != -1 and end_idx > start_idx:
                    json_str = response[start_idx:end_idx]
                    return json.loads(json_str)
        except Exception as e:
            print(f"AI分析失败: {e}")
        
        return {"error": "分析失败"}


# 创建全局学习Agent实例
learning_agent = LearningAgent()


async def initialize_learning_agent():
    """初始化学习Agent"""
    await learning_agent.load_knowledge_base()
    
    # 如果需要更新，立即更新一次
    if await learning_agent.should_update():
        await learning_agent.update_knowledge_base()


# 定期更新任务
async def periodic_update_task():
    """定期更新任务"""
    while True:
        try:
            if await learning_agent.should_update():
                await learning_agent.update_knowledge_base()
            
            # 等待30分钟后再检查（更频繁的检查）
            await asyncio.sleep(1800)
            
        except Exception as e:
            print(f"定期更新任务失败: {e}")
            await asyncio.sleep(1800)  # 出错后等待30分钟
